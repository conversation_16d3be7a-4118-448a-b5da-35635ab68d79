{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^17.3.6", "@angular/cdk": "^15.0.4", "@angular/common": "^17.3.6", "@angular/compiler": "^17.3.6", "@angular/core": "^17.3.6", "@angular/flex-layout": "^15.0.0-beta.42", "@angular/forms": "^17.3.6", "@angular/material": "^15.0.4", "@angular/platform-browser": "^17.3.6", "@angular/platform-browser-dynamic": "^17.3.6", "@angular/router": "^17.3.6", "@auth0/angular-jwt": "^5.2.0", "@fortawesome/fontawesome-free": "^6.5.1", "@microsoft/signalr": "^8.0.7", "@swimlane/ngx-charts": "^20.5.0", "angular-tabler-icons": "^2.7.0", "apexcharts": "^3.35.3", "file-saver": "^2.0.5", "ng-apexcharts": "1.7.1", "ngx-spinner": "^16.0.2", "ngx-toastr": "^18.0.0", "rxjs": "~7.5.0", "sass": "^1.57.1", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.14.4"}, "devDependencies": {"@angular-devkit/build-angular": "^17.2.0", "@angular/cli": "~17.2.0", "@angular/compiler-cli": "^17.2.1", "@types/d3-scale": "^4.0.8", "@types/d3-selection": "^3.0.10", "@types/d3-shape": "^3.1.6", "@types/date-fns": "^2.6.0", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "^5.1.6"}}