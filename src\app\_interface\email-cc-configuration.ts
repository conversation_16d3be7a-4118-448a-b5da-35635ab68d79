// src/app/_interface/email-cc-configuration.ts

export interface EmailCcConfigurationDto {
  id: number;
  configKey: string;
  configName: string;
  isEnabled: boolean;
  description?: string;
  defaultCcEmails: string[];
  defaultBccEmails: string[];
  autoAddCcForOrders: boolean;
  autoAddCcForNotifications: boolean;
  autoAddCcForAlerts: boolean;
  createdAt: Date;
  updatedAt?: Date;
  createdBy?: string;
  updatedBy?: string;
  priority: number;
}

export interface EmailCcConfigurationForCreationDto {
  configKey: string;
  configName: string;
  isEnabled: boolean;
  description?: string;
  defaultCcEmails: string[];
  defaultBccEmails: string[];
  autoAddCcForOrders: boolean;
  autoAddCcForNotifications: boolean;
  autoAddCcForAlerts: boolean;
  priority: number;
}

export interface EmailCcConfigurationForUpdateDto {
  configName: string;
  isEnabled: boolean;
  description?: string;
  defaultCcEmails: string[];
  defaultBccEmails: string[];
  autoAddCcForOrders: boolean;
  autoAddCcForNotifications: boolean;
  autoAddCcForAlerts: boolean;
  priority: number;
}