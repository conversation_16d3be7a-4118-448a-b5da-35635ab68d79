export interface EventExclusionKeywordDto {
  id?: number;
  keyword?: string;
  description?: string;
  isActive?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface EventExclusionKeywordForCreationDto {
  keyword?: string;
  description?: string;
  isActive?: boolean;
}

export interface EventExclusionKeywordForUpdateDto {
  keyword?: string;
  description?: string;
  isActive?: boolean;
}