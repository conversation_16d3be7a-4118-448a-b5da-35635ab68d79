// src/app/_interface/kpi-configuration.ts
export interface KpiConfigurationDto {
  id: number;
  
  // Stars configuration
  stars_EarlyCompletion: number;
  stars_OnTime: number;
  stars_Late1Day: number;
  stars_Late2Days: number;
  stars_Late3OrMoreDays: number;
  
  // Order categorization
  lightOrder_MaxDays: number;
  mediumOrder_MinDays: number;
  mediumOrder_MaxDays: number;
  heavyOrder_MinDays: number;
  
  // HSSL configuration
  hssl_LightOrderFreeCount: number;
  hssl_LightOrderMultiplier: number;
  hssl_MediumOrderMultiplier: number;
  hssl_HeavyOrderMultiplier: number;
  
  // Penalty configuration
  penalty_HeavyError: number;
  penalty_LightError: number;
  penalty_NoError: number;
  
  // Reward/Penalty thresholds
  reward_HighPerformance_MinStars: number;
  reward_HighPerformance_MaxStars: number;
  reward_BaseAmount: number;
  reward_BasePenalty: number;
  penalty_MediumPerformance_MinStars: number;
  penalty_MediumPerformance_MaxStars: number;
  penalty_LowPerformance_MinStars: number;
  penalty_LowPerformance_MaxStars: number;
  penalty_LowPerformance_BaseAmount: number;
  penalty_LowPerformance_MaxPenalty: number;
  
  // Metadata
  createdAt: Date;
  updatedAt?: Date;
  description?: string;
  isActive: boolean;
}

export interface KpiConfigurationForCreationDto {
  // Stars configuration
  stars_EarlyCompletion: number;
  stars_OnTime: number;
  stars_Late1Day: number;
  stars_Late2Days: number;
  stars_Late3OrMoreDays: number;
  
  // Order categorization
  lightOrder_MaxDays: number;
  mediumOrder_MinDays: number;
  mediumOrder_MaxDays: number;
  heavyOrder_MinDays: number;
  
  // HSSL configuration
  hssl_LightOrderFreeCount: number;
  hssl_LightOrderMultiplier: number;
  hssl_MediumOrderMultiplier: number;
  hssl_HeavyOrderMultiplier: number;
  
  // Penalty configuration
  penalty_HeavyError: number;
  penalty_LightError: number;
  penalty_NoError: number;
  
  // Reward/Penalty thresholds
  reward_HighPerformance_MinStars: number;
  reward_HighPerformance_MaxStars: number;
  reward_BaseAmount: number;
  reward_BasePenalty: number;
  penalty_MediumPerformance_MinStars: number;
  penalty_MediumPerformance_MaxStars: number;
  penalty_LowPerformance_MinStars: number;
  penalty_LowPerformance_MaxStars: number;
  penalty_LowPerformance_BaseAmount: number;
  penalty_LowPerformance_MaxPenalty: number;
  
  description?: string;
}

export interface KpiConfigurationForUpdateDto extends KpiConfigurationForCreationDto {
  isActive: boolean;
}