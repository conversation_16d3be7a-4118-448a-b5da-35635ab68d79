export interface UnifiedOrderDto {
  orderCode: string;
  title: string;
  startDate?: string | null;
  endDate?: string | null;
  handler?: string | null;
  source: string;
  status?: string | null;
  timeline?: TimelineStep[] | null;
  dev1?: string | null;
  qc?: string | null;
  code?: string | null;
  sale?: string | null;
}

export interface TimelineStep {
  date: string;
  description: string;
  status: string | null;
}