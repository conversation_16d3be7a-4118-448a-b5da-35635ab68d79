import { HttpInterceptor, HttpRequest, <PERSON>tt<PERSON><PERSON><PERSON><PERSON>, HttpEvent } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable, delay, finalize } from "rxjs";
import { LoadingService } from "../shared/services/loading.service";

@Injectable()
export class LoadingInterceptor implements HttpInterceptor {
    
    constructor(private loadingService: LoadingService) {}

    intercept(req: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {
        
        // <PERSON><PERSON><PERSON> tra xem request có header skipLoading không
        const skipLoading = req.headers.has('skipLoading');
        
        // Nếu có skipLoading header, loại bỏ nó khỏi request và không hiển thị loading
        if (skipLoading) {
            const newReq = req.clone({
                headers: req.headers.delete('skipLoading')
            });
            return next.handle(newReq).pipe(
                delay(1000)
            );
        }
        
        // Hiển thị loading cho các request khác
        this.loadingService.loading();
        return next.handle(req).pipe(
            delay(1000),
            finalize(() => {
                this.loadingService.idle();
            })
        );
    }
}