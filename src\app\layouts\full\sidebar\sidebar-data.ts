import { NavItem } from './nav-item/nav-item';

export const navItems: NavItem[] = [
  // {
  //   navCap: 'Home',
  // },
  // {
  //   displayName: 'Dashboard',
  //   iconName: 'layout-dashboard',
  //   route: '/dashboard',
  // },
  {
    navCap: 'MENU',
  },
  {
    displayName: 'Roles',
    iconName: 'poker-chip',
    route: '/ui-components/roles',
  },
  {
    displayName: 'Users',
    iconName: 'rosette',
    route: '/ui-components/users',
  },
  {
    displayName: 'Categories',
    iconName: 'rosette',
    route: '/ui-components/categories',
  },
  {
    displayName: 'Permissions',
    iconName: 'rosette',
    route: '/ui-components/permissions',
  },
  {
    displayName: 'Customers',
    iconName: 'list',
    route: '/ui-components/lists',
  },
  {
    displayName: 'Audit logs',
    iconName: 'layout-navbar-expand',
    route: '/ui-components/audits',
  },
  {
    displayName: 'Config Calendar Name',
    iconName: 'calendar',
    route: '/ui-components/user-calendars',
  },
  {
    displayName: 'KPI Report',
    iconName: 'chart-bar',
    route: '/ui-components/kpi-report',
  },
  {
    displayName: 'KPI Settings',
    iconName: 'settings',
    route: '/ui-components/kpi-settings',
  },
  {
    displayName: 'Exclude Keywords',
    iconName: 'list',
    route: '/ui-components/event-exclusion-keywords',
  },
  {
    displayName: 'Email CC Configurations',
    iconName: 'mail',
    route: '/ui-components/email-cc-configurations',
  },
  
];


