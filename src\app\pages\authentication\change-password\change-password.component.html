<mat-card class="cardWithShadow theme-card modal-card">
  <mat-card-header>
    <mat-card-title class="m-b-0">Change password</mat-card-title>
    <span class="flex-1-auto"></span>
    <button mat-flat-button color="primary" matTooltipPosition="left" class="m-l-8" matTooltipHideDelay="100000"
      (click)="closeModal()">
      close
    </button>
  </mat-card-header>
  <mat-card-content class="b-t-1">
    <form class="m-t-30" [formGroup]="passwordForm" autocomplete="off" novalidate
      (ngSubmit)="resetPassword(passwordForm.value)">

      <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Previous Password</mat-label>
      <mat-form-field appearance="outline" class="w-100 position-relative" color="primary">
        <div class="password-container">
          <input matInput [type]="hidePassword ? 'password' : 'text'" id="previous" formControlName="previous" class="form-control" />
          <div class="password-toggle-button" (click)="togglePasswordVisibility()">
            <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
          </div>
        </div>
      </mat-form-field>

      <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Password</mat-label>
      <mat-form-field appearance="outline" class="w-100 position-relative" color="primary">
        <div class="password-container">
          <input matInput [type]="hidePassword ? 'password' : 'text'" id="password" formControlName="password" class="form-control" />
          <div class="password-toggle-button" (click)="togglePasswordVisibility()">
            <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
          </div>
        </div>
      </mat-form-field>
      <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Confirm Password</mat-label>
      <mat-form-field appearance="outline" class="w-100 position-relative" color="primary">
        <div class="password-container">
          <input matInput [type]="hidePassword ? 'password' : 'text'" id="confirm" formControlName="confirm" class="form-control" />
          <div class="password-toggle-button" (click)="togglePasswordVisibility()">
            <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
          </div>
        </div>
      </mat-form-field>
  
        <mat-error *ngIf="validateControl('confirm') && hasError('confirm', 'required')">Confirmation is required</mat-error>
        <mat-error *ngIf="hasError('confirm', 'mustMatch')">Passwords must match</mat-error>
    
      <button mat-flat-button color="primary" class="w-100" type="submit" [disabled]="!passwordForm.valid">
        Change password
      </button>
    </form>

  </mat-card-content>
</mat-card>