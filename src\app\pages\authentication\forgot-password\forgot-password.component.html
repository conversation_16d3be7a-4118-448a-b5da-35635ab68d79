<div class="blank-layout-container justify-content-center align-items-center bg-light-primary">
  <div class="position-relative row w-100 h-100 bg-gredient justify-content-center">
    <div class="col-lg-4 d-flex align-items-center">
      <mat-card class="cardWithShadow boxed-auth">
        <mat-card-content class="p-32">
          <div class="text-center">
            <a [routerLink]="['/dashboard']">
              <img src="../../../../OT/assets/images/logos/matech-logo.svg" class="align-middle m-2" style="width: auto; height: 70px;" alt="logo" />
            </a>
          </div>

          <mat-card-title>Forgot password?</mat-card-title>
          <form class="m-t-30" [formGroup]="forgotPasswordForm" autocomplete="off" novalidate
            (ngSubmit)="forgotPassword(forgotPasswordForm.value)">
            <span class="mat-body-1">A password reset link will be sent to your email to reset your password. If you
              don't get an email within a few minutes, please re-try.
            </span>
            <br>
            <br>

            <mat-form-field appearance="outline" class="w-100" color="primary">
              <input matInput placeholder="Email address" type="email" id="email" formControlName="email"
                class="form-control" />
              <mat-error *ngIf="email?.hasError('email') && !email?.hasError('required')">
                Please enter a valid email address
              </mat-error>
              <mat-error *ngIf="email?.hasError('required')">
                Email is required
              </mat-error>
            </mat-form-field>

            <div class="d-flex align-items-center m-b-12">
              <button mat-flat-button color="warn" class="m-t-8" [routerLink]="['/authentication/login']">
                Back to login
              </button>
              <span class="flex-1-auto"></span>
              <button mat-flat-button color="primary" class="m-t-8" type="submit"
                [disabled]="!forgotPasswordForm.valid">
                Submit
              </button>
            </div>
            
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>