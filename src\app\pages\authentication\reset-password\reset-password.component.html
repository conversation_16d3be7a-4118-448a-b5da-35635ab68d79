<div class="blank-layout-container justify-content-center align-items-center bg-light-primary">
  <div class="position-relative row w-100 h-100 bg-gredient justify-content-center">
    <div class="col-lg-4 d-flex align-items-center">
      <mat-card class="cardWithShadow boxed-auth">
        <mat-card-content class="p-32">
          <div class="text-center">
            <a>
              <img src="../../../../OT/assets/images/logos/matech-logo.svg" class="align-middle m-2" style="width: auto; height: 70px;" alt="logo" />
            </a>
          </div>
          <mat-card-title>Change password</mat-card-title>
          <form class="m-t-30" [formGroup]="resetPasswordForm" autocomplete="off" novalidate
            (ngSubmit)="resetPassword(resetPasswordForm.value)">

            <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Password</mat-label>
            <mat-form-field appearance="outline" class="w-100 position-relative" color="primary">
              <div class="password-container">
                <input matInput [type]="hidePassword ? 'password' : 'text'" id="password" formControlName="password" class="form-control" />
                <div class="password-toggle-button" (click)="togglePasswordVisibility()">
                  <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
                </div>
              </div>
            </mat-form-field>
            
            <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Confirm Password</mat-label>
            <mat-form-field appearance="outline" class="w-100 position-relative" color="primary">
              <div class="password-container">
                <input matInput [type]="hidePassword ? 'password' : 'text'" id="confirm" formControlName="confirm" class="form-control" />
                <div class="password-toggle-button" (click)="togglePasswordVisibility()">
                  <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
                </div>
              </div>
            </mat-form-field>
            <mat-error *ngIf="validateControl('confirm') && hasError('confirm', 'required')">Confirmation is
              required</mat-error>
            <mat-error *ngIf="hasError('confirm', 'mustMatch')">Passwords must match</mat-error>

            <button mat-flat-button color="primary" class="w-100" type="submit" [disabled]="!resetPasswordForm.valid">
              Change password
            </button>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>