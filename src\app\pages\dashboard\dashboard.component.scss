/* Container chính */
.dashboard-container {
  min-height: 100vh;
  background-color: #f9fafb;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  padding: 2rem 0;
}

/* Header */
.dashboard-header {
  text-align: center;
  margin-bottom: 2rem;
}

.dashboard-header .logo img {
  height: 70px;
  width: auto;
}

/* Slider styles */
.slider-container {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 1rem;
  box-sizing: border-box;
}

.slider-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(31, 97, 175, 0.12);
  background: #fff;
  width: 100%;
  aspect-ratio: 1132 / 450;
  max-height: 400px;
}

.slider-track {
  display: flex;
  transition: transform 0.5s ease-in-out;
  height: 100%;
}

.slide {
  min-width: 100%;
  height: 100%;
}

.slide-link {
  display: block;
  height: 100%;
  text-decoration: none;
}

.img-inner {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.slide-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.slider-dots {
  position: absolute;
  bottom: 15px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 8px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
}

.dot.active {
  background-color: #fff;
}

/* Welcome Message Styles */
.welcome-container {
  width: 100%;
  max-width: 1000px;
  margin: 2rem auto 0;
  padding: 0 1rem;
  box-sizing: border-box;
}

.welcome-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(31, 97, 175, 0.08);
  padding: 2rem;
  text-align: center;
}

.welcome-title {
  color: #1f61af;
  font-size: 2rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.welcome-message {
  color: #555;
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.welcome-info {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  align-items: center;
  color: #666;
}

.info-item mat-icon {
  margin-right: 0.5rem;
  color: #1f61af;
}

.welcome-footer {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #eee;
  color: #777;
  font-size: 0.9rem;
}

.welcome-footer a {
  color: #1f61af;
  text-decoration: none;
  font-weight: 500;
}

.welcome-footer a:hover {
  text-decoration: underline;
}

/* Responsive styles */
@media (max-width: 768px) {
  .dashboard-header {
    margin-bottom: 1.5rem;
  }
  
  .dashboard-header .logo img {
    height: 60px;
  }
  
  .slider-wrapper {
    max-height: 300px;
  }
  
  .welcome-title {
    font-size: 1.7rem;
  }
  
  .welcome-message {
    font-size: 1rem;
  }
  
  .welcome-info {
    flex-direction: column;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .dashboard-header .logo img {
    height: 50px;
  }
  
  .slider-wrapper {
    max-height: 200px;
  }
  
  .dot {
    width: 10px;
    height: 10px;
  }
  
  .welcome-card {
    padding: 1.5rem;
  }
  
  .welcome-title {
    font-size: 1.5rem;
  }
}
