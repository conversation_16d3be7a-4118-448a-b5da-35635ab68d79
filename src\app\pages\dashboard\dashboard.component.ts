import {
  Component,
  ViewEncapsulation,
  OnInit
} from '@angular/core';
import { RepositoryService } from 'src/app/shared/services/repository.service';
import { AuthenticationService } from 'src/app/shared/services/authentication.service';

interface Slide {
  image: string;
  alt: string;
  link: string;
}

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class AppDashboardComponent implements OnInit {
  // User info
  public userName: string = '';
  public lastLogin: Date | null = null;
  public today: Date = new Date();
  
  // Slider properties
  public currentSlide: number = 0;
  public slides: Slide[] = [
    {
      image: 'https://atpro.com.vn/wp-content/uploads/2025/04/banner-may-tinh-cong-nghiep-29-04-1024x346.jpg',
      alt: 'm<PERSON><PERSON> t<PERSON>h công nghiệp',
      link: 'https://atpro.com.vn/may-tinh-cong-nghiep/'
    },
    {
      image: 'https://atpro.com.vn/wp-content/uploads/2025/01/he-thong-xep-hang-tu-dong-AT-QMS05-5.jpg',
      alt: 'Hệ thống xếp hàng tự động – AT-QMS05',
      link: 'https://atpro.com.vn/san-pham/he-thong-xep-hang-tu-dong-cao-cap/'
    },
    {
      image: 'https://atpro.com.vn/wp-content/uploads/2025/04/dong-ho-led-treo-tuong.jpg',
      alt: 'Đồng hồ led treo tường',
      link: 'https://atpro.com.vn/danh-muc/dong-ho-dien-tu/'
    }
  ];

  constructor(
    private repoService: RepositoryService,
    private authService: AuthenticationService
  ) { }

  ngOnInit() {
    this.startSlideshow();
    this.getUserInfo();
  }

  getUserInfo() {
    // Lấy thông tin người dùng từ localStorage hoặc service
    const user = this.authService.loadCurrentUserName();
    if (user) {
      this.userName = user.firstName || user.userName || '';
      // Giả lập thời gian đăng nhập cuối cùng (thay bằng API thực tế nếu có)
      this.lastLogin = new Date(Date.now() - 24 * 60 * 60 * 1000); // 1 ngày trước
    }
  }

  // Slider methods
  startSlideshow() {
    setInterval(() => {
      this.nextSlide();
    }, 5000);
  }

  nextSlide() {
    this.currentSlide = (this.currentSlide + 1) % this.slides.length;
  }

  prevSlide() {
    this.currentSlide = (this.currentSlide - 1 + this.slides.length) % this.slides.length;
  }

  goToSlide(index: number) {
    this.currentSlide = index;
  }
}


