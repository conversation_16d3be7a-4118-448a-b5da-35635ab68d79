<div class="search-container" [ngClass]="{'empty': !orderDetails}">
  <div class="search-header w-100">
    <div class="logo">
      <a href="https://atpro.com.vn/" target="_blank">
        <img src="../../../../ot/assets/images/logos/matech-logo.svg" alt="Logo">
      </a>
    </div>
  </div>

  <!-- Image Slider Section -->
  <div class="slider-container w-100" *ngIf="!orderDetails">
    <div class="slider-wrapper">
      <div class="slider-track" [style.transform]="'translateX(-' + currentSlide * 100 + '%)'">
        <div class="slide" *ngFor="let slide of slides; let i = index">
          <div class="slide-link">
            <div class="img-inner dark">
              <img
                [src]="slide.image"
                [alt]="slide.alt"
                class="slide-image"
                loading="lazy">
            </div>
          </div>
        </div>
      </div>
       
      <!-- <PERSON> Arrows - Removed -->
      
      <!-- Dots Indicator -->
      <div class="slider-dots">
        <button 
          *ngFor="let slide of slides; let i = index" 
          class="dot" 
          [class.active]="i === currentSlide"
          (click)="goToSlide(i)">
        </button>
      </div>
    </div>
  </div>
      <div class="search-title w-100">
              <h2>Tìm kiếm Thông Tin Đơn Hàng</h2>
      </div>
  <div class="search-form w-100">
    <div class="search-form-container">
      <div class="search-input-container">
        <mat-form-field appearance="outline">
          <mat-label>Vui lòng mã đơn hàng...</mat-label>
          <input matInput [(ngModel)]="searchTerm" placeholder="Mã Đơn Hàng" (keyup.enter)="search()">
          <button *ngIf="searchTerm" matSuffix mat-icon-button aria-label="Xóa" (click)="clearSearch()">
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <div class="search-button-container">
  <button 
    mat-raised-button 
    color="primary" 
    style="background-color: #1f61af; color: white;" 
    (click)="search()"
    [disabled]="isLoading">
    
    <!-- Hiển thị spinner khi đang loading -->
    <mat-spinner 
      *ngIf="isLoading" 
      diameter="20" 
      color="accent"
      style="display: inline-block; margin-right: 8px;">
    </mat-spinner>
    
    <!-- Hiển thị icon search khi không loading -->
    <mat-icon *ngIf="!isLoading" style="margin-right: 8px;">search</mat-icon>
    
    <!-- Text thay đổi theo trạng thái -->
    {{ isLoading ? 'Đang tìm kiếm...' : 'Tìm kiếm' }}
  </button>
</div>
      </div>
<div class="search-results-container" *ngIf="isLoading || orderDetails">
  
  <!-- Loading overlay -->
      <!-- Order details section remains unchanged -->
      <div class="order-details w-100" *ngIf="orderDetails&& !isLoading">
        <mat-card class="w-100 cardWithShadow">
          <mat-card-content>
            <h2 class="text-center">Thông tin đơn hàng</h2>
            <div class="order-info">
              <!-- Tên đơn hàng -->
              <div class="title-container text-center mb-3">
                <h3 class="title-label">Tên đơn hàng</h3>
                <div class="title-value">{{orderDetails.title}}</div>
              </div>

              <!-- Thời gian bắt đầu và kết thúc -->
              <div class="date-container">
                <div class="date-item" *ngIf="orderDetails.startDate">
                  <span class="label">Thời gian bắt đầu:</span>
                  <span class="value">{{orderDetails.startDate | date:'dd/MM/yyyy'}}</span>
                </div>
                <div class="date-arrow" *ngIf="orderDetails.startDate && orderDetails.endDate">
                  <mat-icon>arrow_forward</mat-icon>
                </div>
                <div class="date-item" *ngIf="orderDetails.endDate">
                  <span class="label">Thời gian dự kiến hoàn thành:</span>
                  <span class="value">{{orderDetails.endDate | date:'dd/MM/yyyy'}}</span>
                </div>
              </div>
              
              <!-- Thông tin nhân sự phụ trách -->
              <div class="staff-container" *ngIf="orderDetails.source != 'Sheets'">
                <!-- Sales phụ trách -->
                <div class="staff-item" *ngIf="orderDetails.sale">
                  <span class="label">Sales phụ trách:</span>
                  <span class="value">{{orderDetails.sale}}</span>
                </div>
                
                <!-- Kỹ sư triển khai -->
                <div class="staff-item" *ngIf="orderDetails.handler">
                  <span class="label">Kỹ sư triển khai:</span>
                  <span class="value">{{orderDetails.handler}}</span>
                </div>
                
                <!-- Kỹ sư QC -->
                <div class="staff-item" *ngIf="orderDetails.qc">
                  <span class="label">Kỹ sư QC:</span>
                  <span class="value">{{orderDetails.qc}}</span>
                </div>
              </div>
            </div>

            <!-- Hiển thị thông tin cho Sheets -->
            <div class="sheets-info" *ngIf="orderDetails.source === 'Sheets'">
              <h3 class="mt-4 text-center">Thông tin chi tiết</h3>
              <div class="info-list">
                <div class="info-item" *ngIf="orderDetails.dev1">
                  <span class="label">Kỹ Thuật Viên:</span>
                  <span class="value">{{orderDetails.dev1}}</span>
                </div>
                <div class="info-item" *ngIf="orderDetails.handler">
                  <span class="label">Coder:</span>
                  <span class="value">{{orderDetails.handler}}</span>
                </div>
                <div class="info-item" *ngIf="orderDetails.qc">
                  <span class="label">Người QC:</span>
                  <span class="value">{{orderDetails.qc}}</span>
                </div>
                <div class="info-item" *ngIf="orderDetails.sale">
                  <span class="label">Sales:</span>
                  <span class="value">{{orderDetails.sale}}</span>
                </div>
                <div class="info-item" *ngIf="orderDetails.status">
                  <span class="label">Trạng thái:</span>
                  <span class="value">{{orderDetails.status}}</span>
                </div>
              </div>
            </div>

            <!-- Hiển thị thông tin cho Calendar -->
            <div class="calendar-info" *ngIf="orderDetails.source === 'Calendar' && orderDetails.timeline && orderDetails.timeline.length > 0">
              <h3 class="mt-4 text-center">Tổng quan tiến độ</h3>
              
              <!-- Hiển thị thông báo đơn giản nếu đơn hàng đã hoàn thành -->
              <div *ngIf="isOrderCompleted()" class="completed-order-message">
                <mat-icon class="completed-icon">check_circle</mat-icon>
                <h3>Đơn hàng đã hoàn thành</h3>
                <p *ngIf="isOrderDelayed()">{{getDelayMessage()}}</p>
              </div>
              
              <!-- Hiển thị chi tiết tiến độ nếu đơn hàng chưa hoàn thành -->
              <div *ngIf="!isOrderCompleted()">
                <!-- Thanh tiến độ -->
                <div class="progress-container w-100">
                  <div class="progress-percentage" [ngClass]="{
                    'text-success': !isOrderDelayed() && !isBehindSchedule(),
                    'text-behind': isBehindSchedule() && !isOrderDelayed(),
                    'text-warning': isOrderDelayed() && !getDelayInfo().isOverdue,
                    'text-danger': isOrderDelayed() && getDelayInfo().isOverdue
                  }">
                    {{getProgressPercentage() | number:'1.0-0'}}%
                  </div>
                  <div class="progress-bar">
                    <div class="progress-fill" [ngClass]="{
                      'fill-success': !isOrderDelayed() && !isBehindSchedule(),
                      'fill-behind': isBehindSchedule() && !isOrderDelayed(),
                      'fill-warning': isOrderDelayed() && !getDelayInfo().isOverdue,
                      'fill-danger': isOrderDelayed() && getDelayInfo().isOverdue
                    }" [style.width]="getProgressPercentage() + '%'"></div>
                  </div>
                </div>
                <!-- Thẻ trạng thái tiến độ -->
                <div class="progress-status-card">
                  <div class="progress-status" [ngClass]="{
                    'status-on-track': !isOrderDelayed() && !isBehindSchedule(),
                    'status-behind': isBehindSchedule() && !isOrderDelayed(),
                    'status-at-risk': isOrderDelayed() && !getDelayInfo().isOverdue,
                    'status-overdue': isOrderDelayed() && getDelayInfo().isOverdue
                  }">
                    <div class="status-icon">
                      <mat-icon>
                        {{!isOrderDelayed() && !isBehindSchedule() ? 'schedule' : 
                          (isBehindSchedule() && !isOrderDelayed() ? 'hourglass_empty' : 
                          (isOrderDelayed() && !getDelayInfo().isOverdue ? 'warning' : 'error'))}}
                      </mat-icon>
                    </div>
                    <div class="status-text">
                      <h4>{{getProgressStatus()}}</h4>
                      <p *ngIf="isOrderDelayed()">{{getDelayMessage()}}</p>
                      <p *ngIf="isBehindSchedule() && !isOrderDelayed()">{{getBehindScheduleMessage()}}</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Timeline luôn hiển thị, bất kể đơn hàng đã hoàn thành hay chưa -->
              <h3 class="mt-4 text-center">Tiến độ đơn hàng</h3>

              <!-- Horizontal Timeline (Desktop) -->
              <div class="timeline-horizontal w-100">
                <div class="timeline-track">
                  <div class="timeline-line"></div>
                  <div class="timeline-items">
                    <div class="timeline-item" *ngFor="let item of orderDetails.timeline; let i = index; let isLast = last">
                      <div class="timeline-badge" 
                           [ngClass]="{
                             'completed': item.status === 'V', 
                             'pending': item.status !== 'V'
                           }">
                        <mat-icon *ngIf="item.status === 'V'">check</mat-icon>
                        <mat-icon *ngIf="item.status !== 'V'">schedule</mat-icon>
                      </div>
                      <div class="timeline-content">
                        <div class="timeline-date">{{item.date | date:'dd/MM/yyyy'}}</div>
                        <div class="timeline-panel">
                          <div class="timeline-status" [ngClass]="getStatusClass(item.status)">
                            {{getStatusText(item.status)}}
                          </div>
                          <div class="timeline-description">
                            {{item.description}}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Vertical Timeline (Mobile) -->
              <div class="timeline-vertical w-100">
                <div class="timeline-item" *ngFor="let item of orderDetails.timeline; let i = index; let isLast = last">
                  <div class="timeline-badge" 
                       [ngClass]="{
                         'completed': item.status === 'V', 
                         'pending': item.status !== 'V'
                       }">
                    <mat-icon *ngIf="item.status === 'V'">check</mat-icon>
                    <mat-icon *ngIf="item.status !== 'V'">schedule</mat-icon>
                  </div>
                  <div class="timeline-panel">
                    <div class="timeline-heading">
                      <h4 class="timeline-title">{{item.date | date:'dd/MM/yyyy'}}</h4>
                      <div class="timeline-status" [ngClass]="getStatusClass(item.status)">
                        {{getStatusText(item.status)}}
                      </div>
                    </div>
                    <div class="timeline-body">
                      <p>{{item.description}}</p>
                    </div>
                  </div>
                  <div class="timeline-line" *ngIf="!isLast"></div>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </div>
</div>
</div>
<div class="footer">
  <div class="container">
    <div class="footer-content">
      <div class="footer-copyright">
        <span>Copyright 2025 ©. Công ty Cổ Phần Giải Pháp Kỹ Thuật Ấn Tượng. GPDKKD: 0309893542</span>
      </div>
      <div class="footer-contact">
        <span class="footer-label">Hotline:</span>
        <a href="tel:02838425001" class="footer-phone">
          <mat-icon>phone</mat-icon>
          028.3842.5001
        </a>
      </div>
    </div>
  </div>
</div>




