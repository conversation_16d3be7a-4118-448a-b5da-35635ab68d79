/* <PERSON><PERSON><PERSON> b<PERSON><PERSON> không có overflow ngang */
body, html {
  overflow-x: hidden;
}
/* CSS cho loading effects */

/* Loading overlay */
.loading-overlay {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  backdrop-filter: blur(5px);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-text {
  margin: 0;
  color: #666;
  font-size: 16px;
  font-weight: 500;
}

/* Button loading state */
.search-button-container button[disabled] {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Skeleton loading cho các card (tùy chọn) */
.skeleton-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Custom spinner nếu không dùng Material */
.custom-spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1f61af;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  display: inline-block;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pulse animation cho button */
.search-button-loading {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
/* Container chính */
.search-container {
  min-height: 100vh;
  padding-bottom: 100px;
  background-color: #f9fafb; /* Thay đổi từ trắng sang xám rất nhạt */
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.search-container.empty {
  justify-content: center;
}

/* Header tìm kiếm */
.search-header {
  text-align: center;
  margin-bottom: 2rem;
}

.search-header .logo img {
  height: 70px;
  width: auto;
}

/* Responsive cho mobile */
@media (max-width: 768px) {
  .search-container {
    padding-top: 1rem; /* Giảm padding phía trên */
  }
  
  .search-header {
    margin-bottom: 1rem; /* Giảm margin dưới logo */
  }
  
  .search-header .logo img {
    height: 60px; /* Giảm kích thước logo trên mobile */
  }
  
  .slider-container {
    margin-bottom: 1rem; /* Giảm margin dưới slider */
  }
  
  .search-title {
    margin: 1rem 0; /* Giảm margin của tiêu đề */
  }
  
  /* Tăng khoảng cách giữa các phần nội dung */
  .order-info {
    margin-top: 1rem;
  }
}

/* Điều chỉnh thêm cho màn hình rất nhỏ */
@media (max-width: 375px) {
  .search-container {
    padding-top: 0.5rem; /* Giảm thêm padding phía trên */
  }
  
  .search-header {
    margin-bottom: 0.75rem; /* Giảm thêm margin dưới logo */
  }
  
  .search-header .logo img {
    height: 50px; /* Giảm thêm kích thước logo */
  }
}

/* ================ SLIDER STYLES ================ */
.slider-container {
  width: 100%;
  max-width: 900px; /* Giảm từ 1200px xuống 900px */
  margin: 0 auto 2rem;
  padding: 0 1rem;
  box-sizing: border-box;
}

.slider-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(31, 97, 175, 0.12);
  background: #fff;
  width: 100%;
  aspect-ratio: 1132 / 450; /* Tỷ lệ gốc của banner */
  max-height: 250px;
  min-height: 120px;
}

.slider-track {
  display: flex;
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
  height: 100%;
  position: relative;
  /* Đảm bảo slides không bị nén */
  flex-wrap: nowrap;
}

.slide {
  min-width: 100%;
  width: 100%;
  height: 100%;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  /* Đảm bảo mỗi slide độc lập */
  box-sizing: border-box;
}

.slide-link {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
  transition: transform 0.3s ease;
  position: relative;
  overflow: hidden;
  /* Thêm background để khớp với màu nền */
  background: #fff;
}

.slide-link:hover {
  transform: scale(1.01);
}

.img-inner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 8px;
  /* Thay đổi background để khớp với màu nền của slider */
  background: #fff;
}

.slide-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* Giữ object-fit: contain để không làm vỡ hình */
  //object-fit: contain;
  object-position: center;
  transition: transform 0.5s ease;
  border-radius: 8px;
  /* Thay đổi background thành trong suốt */
  background: transparent;
}

.slide-link:hover .slide-image {
  transform: scale(1.03);
}

/* Navigation Arrows */
.slider-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.slider-nav:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-50%) scale(1.1);
}

.slider-nav:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: translateY(-50%) scale(0.9);
}

.slider-nav.prev {
  left: 20px;
}

.slider-nav.next {
  right: 20px;
}

.slider-nav mat-icon {
  font-size: 24px;
  color: #333;
}

/* Dots Indicator */
.slider-dots {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12px;
  z-index: 10;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: scale(1.2);
}

.dot.active {
  background: #fff;
  transform: scale(1.3);
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
}

/* Auto-slide animation */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* ================ END SLIDER STYLES ================ */

/* Form tìm kiếm */
.search-form {
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.search-form-container {
  width: 100%;
  padding: 0.5rem;
  box-sizing: border-box;
}

.search-input-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  width: 100%;
}

.search-input-container mat-form-field {
  width: 100%;
  max-width: 500px;
}

.search-input-container .search-button-container {
  width: 100%;
  max-width: 500px;
  display: flex;
  justify-content: center;
}

.search-input-container .search-button-container button {
  min-width: 120px;
}

/* Chi tiết đơn hàng */
.order-details {
  margin-top: 2rem;
  width: 100%;
}

.order-details h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #333;
  text-align: center;
}

.order-details h3 {
  font-size: 1.2rem;
  margin: 1.5rem 0 1rem;
  color: #333;
  text-align: center;
}

/* Đảm bảo mat-card trong order-details có chiều rộng đầy đủ */
:host ::ng-deep mat-card {
  width: 100% !important;
  box-sizing: border-box !important;
  margin: 0 !important;
}

:host ::ng-deep mat-card-content {
  width: 100% !important;
  padding-bottom: 100px !important;
}

/* Thông tin đơn hàng */
.order-info {
  background-color: #ffffff; /* Giữ màu trắng cho các phần tử nội dung */
  border-radius: 4px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px rgba(31, 97, 175, 0.08); /* Thêm shadow với màu chủ đạo */
}

.title-container {
  margin-bottom: 1.5rem;
  text-align: center;
}

.title-container .title-label {
  font-size: 1.1rem;
  color: #555;
  margin-bottom: 0.5rem;
}

.title-container .title-value {
  font-weight: 500;
  color: #333;
  margin-top: 0.5rem;
  font-size: 24px;
}

.date-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.date-container .date-item {
  display: flex;
  flex-direction: column;
  min-width: 150px;
  text-align: center;
}

.date-container .date-item .label {
  font-weight: 500;
  color: #555;
  margin-bottom: 0.25rem;
}

.date-container .date-item .value {
  color: #333;
}

.date-container .date-arrow {
  display: flex;
  align-items: center;
  color: #666;
  margin: 0 1rem;
}

/* Thanh tiến độ */
.progress-container {
  position: relative;
  width: 100%;
  margin: 2rem 0;
}

.progress-bar {
  position: relative;
  height: 24px;
  background-color: #e0e0e0;
  border-radius: 12px;
  overflow: hidden;
}

.progress-fill {
  position: absolute;
  height: 100%;
  background-color: #4caf50;
  border-radius: 12px;
  transition: width 0.5s ease-in-out;
}

.progress-fill.fill-success {
  background-color: #4caf50;
}

.progress-fill.fill-behind,
.progress-fill.fill-warning {
  background-color: #ff9800;
}

.progress-fill.fill-danger {
  background-color: #f44336;
}

.progress-percentage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-weight: 500;
  font-size: 0.9rem;
  color: #fff;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
  z-index: 2;
}

/* Thẻ trạng thái tiến độ */
.progress-status-card {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 1.5rem 0;
}

.progress-status {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  max-width: 90%;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-status.status-completed {
  background-color: rgba(76, 175, 80, 0.1);
}

.progress-status.status-on-track {
  background-color: rgba(33, 150, 243, 0.1);
}

.progress-status.status-behind,
.progress-status.status-at-risk {
  background-color: rgba(255, 152, 0, 0.1);
}

.progress-status.status-overdue {
  background-color: rgba(244, 67, 54, 0.1);
}

.status-icon {
  margin-right: 1rem;
}

.status-icon mat-icon {
  font-size: 24px;
  height: 24px;
  width: 24px;
}

.status-text h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 500;
}

.status-text p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
}

/* Timeline ngang (Desktop) */
.timeline-horizontal {
  display: block;
  width: 100%;
  margin: 2rem 0;
  padding: 1rem 0;
  overflow-x: auto;
  margin-bottom: 80px;
}

.timeline-horizontal .timeline-track {
  position: relative;
  min-width: 100%;
  padding: 2rem 0;
}

.timeline-horizontal .timeline-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #e0e0e0;
  transform: translateY(-50%);
  z-index: 1;
}

.timeline-horizontal .timeline-items {
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.timeline-horizontal .timeline-item {
  flex: 1;
  min-width: 150px;
  max-width: 250px;
  padding: 0 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.timeline-horizontal .timeline-item:first-child {
  padding-left: 0;
}

.timeline-horizontal .timeline-item:last-child {
  padding-right: 0;
}

.timeline-horizontal .timeline-badge {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.timeline-horizontal .timeline-badge.completed {
  background-color: #4caf50;
  color: white;
}

.timeline-horizontal .timeline-badge.pending {
  background-color: #f44336;
  color: white;
}

.timeline-horizontal .timeline-content {
  width: 100%;
  text-align: center;
}

.timeline-horizontal .timeline-date {
  font-weight: 500;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.timeline-horizontal .timeline-panel {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.timeline-horizontal .timeline-status {
  font-size: 0.85rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  display: inline-block;
  margin-bottom: 0.5rem;
}

.timeline-horizontal .timeline-status.status-active {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.timeline-horizontal .timeline-status.status-inactive {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.timeline-horizontal .timeline-description {
  font-size: 0.85rem;
  color: #555;
  word-break: break-word;
}

/* Timeline dọc (Mobile) */
.timeline-vertical {
  display: none;
  position: relative;
  padding: 1rem 0;
  margin: 0 0.5rem;
  margin-bottom: 60px;
}

.timeline-vertical .timeline-item {
  position: relative;
  margin-bottom: 1.5rem;
  display: flex;
  width: 100%;
}

.timeline-vertical .timeline-badge {
  min-width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  flex-shrink: 0;
}

.timeline-vertical .timeline-badge.completed {
  background-color: #4caf50;
  color: white;
}

.timeline-vertical .timeline-badge.pending {
  background-color: #f44336;
  color: white;
}

.timeline-vertical .timeline-badge mat-icon {
  font-size: 18px;
  height: 18px;
  width: 18px;
  line-height: 18px;
}

.timeline-vertical .timeline-panel {
  flex: 1;
  margin-left: 1rem;
  padding: 1rem;
  border-radius: 4px;
  background-color: #f9f9f9;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  word-break: break-word;
}

.timeline-vertical .timeline-heading {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.timeline-vertical .timeline-title {
  margin: 0;
  font-size: 0.95rem;
  font-weight: 500;
  color: #333;
}

.timeline-vertical .timeline-status {
  font-size: 0.85rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
}

.timeline-vertical .timeline-status.status-active {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.timeline-vertical .timeline-status.status-inactive {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.timeline-vertical .timeline-body p {
  margin: 0;
  color: #555;
  font-size: 0.9rem;
  line-height: 1.4;
}

.timeline-vertical .timeline-line {
  position: absolute;
  top: 36px;
  left: 18px;
  bottom: -24px;
  width: 2px;
  background-color: #e0e0e0;
  z-index: 1;
}

/* Footer */
.footer {
  width: 100%;
  background-color: #ffffff;
  border-top: 1px solid rgba(31, 97, 175, 0.1);
  padding: 8px 0; /* Giảm padding từ 10px xuống 8px */
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 1000;
  box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.footer-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

.footer-contact {
  display: flex;
  align-items: center;
  gap: 5px;
  white-space: nowrap; /* Ngăn số điện thoại bị xuống dòng */
}

.footer-label {
  font-weight: 500;
  color: #495057;
}

.footer-phone {
  color: #1f61af; /* Thay đổi màu để phù hợp với theme */
  font-weight: 600;
  text-decoration: none;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.footer-phone mat-icon {
  font-size: 16px;
  height: 16px;
  width: 16px;
  line-height: 16px;
}

.footer-phone:hover {
  color: #3a0ca3;
  text-decoration: underline;
}

.footer-copyright {
  color: #6c757d;
  font-size: 0.85rem; /* Giảm font size */
  text-align: left;
  flex: 1; /* Cho phép copyright chiếm không gian còn lại */
}

/* Responsive */
@media (max-width: 768px) {
  .footer {
    padding: 6px 0; /* Giảm padding trên mobile */
  }
  
  .footer-content {
    flex-direction: column-reverse; /* Đảo ngược thứ tự: số điện thoại lên trên, copyright xuống dưới */
    gap: 4px;
    align-items: center;
  }
  
  .footer-copyright {
    text-align: center;
    font-size: 0.75rem; /* Giảm font size trên mobile */
    margin-top: 2px;
  }
  
  .footer-phone mat-icon {
    font-size: 14px;
    height: 14px;
    width: 14px;
    line-height: 14px;
  }
  
  .footer-label {
    font-size: 0.85rem;
  }
  
  .footer-phone {
    font-size: 0.85rem;
  }
}

/* Thông tin Sheets */
.sheets-info {
  margin: 2rem auto;
  max-width: 600px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40px;
}

.sheets-info h3 {
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 1.5rem;
  text-align: center;
}

.info-list {
  background-color: #ffffff; /* Giữ màu trắng cho các phần tử nội dung */
  border-radius: 8px;
  padding: 1.5rem;
  width: 100%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e0e0e0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item .label {
  font-weight: 500;
  color: #555;
  min-width: 120px;
  font-size: 0.95rem;
}

.info-item .value {
  color: #333;
  font-size: 0.95rem;
  flex: 1;
  word-break: break-word;
}

.info-item.status .value {
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  display: inline-block;
}

.info-item.status .value.KHÔNG-XÁC-ĐỊNH {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

/* Style cho thông báo đơn hàng đã hoàn thành */
.completed-order-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  margin: 1.5rem 0;
  border-radius: 8px;
  text-align: center;
  /* Sử dụng màu xanh cho tất cả trạng thái hoàn thành */
  background-color: rgba(76, 175, 80, 0.1);
}

.completed-order-message .completed-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
  margin-bottom: 1rem;
  /* Sử dụng màu xanh cho icon */
  color: #4caf50;
}

.completed-order-message h3 {
  font-weight: 500;
  margin: 0 0 0.5rem 0;
  /* Sử dụng màu xanh cho tiêu đề */
  color: #4caf50;
}

.completed-order-message p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
}

/* Xóa bỏ các class không cần thiết */
/* 
.completed-order-message.completed-on-time {
  background-color: rgba(76, 175, 80, 0.1);
}

.completed-order-message.completed-late {
  background-color: rgba(255, 152, 0, 0.1);
}

.completed-order-message.completed-on-time .completed-icon {
  color: #4caf50;
}

.completed-order-message.completed-late .completed-icon {
  color: #ff9800;
}

.completed-order-message.completed-on-time h3 {
  color: #4caf50;
}

.completed-order-message.completed-late h3 {
  color: #ff9800;
}
*/

/* ================ MOBILE RESPONSIVE FOR SLIDER ================ */
@media (max-width: 768px) {
  .search-container {
    padding: 0.5rem;
  }

  .search-container.empty {
    justify-content: center;
  }

  .search-form-container {
    padding: 0.75rem;
  }

  .search-input-container {
    width: 100%;
  }

  .search-input-container mat-form-field,
  .search-input-container .search-button-container {
    max-width: 100%;
  }

  .search-input-container .search-button-container button {
    width: 100%;
  }

  /* Slider responsive */
  .slider-container {
    padding: 0 0.5rem;
    margin-bottom: 1.5rem;
  }

  .slider-wrapper {
    /* Tỷ lệ 3:1 cho mobile để giảm chiều cao hơn nữa */
    aspect-ratio: 3 / 1;
    max-height: 200px; /* Giảm từ 300px xuống 200px */
    min-height: 120px; /* Giảm từ 150px xuống 120px */
  }

  .slider-nav {
    width: 40px;
    height: 40px;
  }

  .slider-nav.prev {
    left: 10px;
  }

  .slider-nav.next {
    right: 10px;
  }

  .slider-nav mat-icon {
    font-size: 20px;
  }

  .slider-dots {
    bottom: 15px;
    gap: 8px;
  }

  .dot {
    width: 10px;
    height: 10px;
  }

  .timeline-horizontal {
    display: none;
  }

  .timeline-vertical {
    display: block;
    width: 100%;
    margin: 0;
    padding: 0;
  }

  .timeline-vertical .timeline-panel {
    width: calc(100% - 50px);
    max-width: calc(100% - 50px);
    box-sizing: border-box;
  }

  .order-info {
    width: 100%;
    box-sizing: border-box;
    padding: 0.75rem;
  }

  .progress-status {
    flex-direction: column;
    text-align: center;
  }

  .progress-status .status-icon {
    margin: 0 0 0.75rem 0;
  }

  .date-container {
    flex-direction: column;
    align-items: center;
  }

  .date-container .date-arrow {
    transform: rotate(90deg);
    margin: 0.5rem 0;
  }

  .sheets-info {
    max-width: 100%;
    padding: 0 0.5rem;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
    padding: 0.75rem 0;
  }

  .info-item .label {
    min-width: unset;
    font-size: 0.9rem;
  }

  .info-item .value {
    font-size: 0.9rem;
  }
}

/* Màn hình rất nhỏ (max-width: 375px) */
@media (max-width: 375px) {
  .search-container {
    padding: 0.25rem;
  }

  .search-form-container {
    padding: 0.75rem;
  }

  .slider-container {
    padding: 0 0.25rem;
  }

  .slider-wrapper {
    /* Tỷ lệ 3.5:1 cho màn hình rất nhỏ */
    aspect-ratio: 3.5 / 1;
    max-height: 150px; /* Giảm xuống 150px */
    min-height: 100px; /* Giảm xuống 100px */
  }

  .slider-nav {
    width: 35px;
    height: 35px;
  }

  .slider-nav mat-icon {
    font-size: 18px;
  }

  .timeline-vertical .timeline-panel {
    padding: 0.5rem;
    margin-left: 0.5rem;
  }

  .timeline-vertical .timeline-badge {
    min-width: 28px;
    height: 28px;
  }

  .timeline-vertical .timeline-badge mat-icon {
    font-size: 16px;
    height: 16px;
    width: 16px;
    line-height: 16px;
  }

  .timeline-vertical .timeline-line {
    top: 28px;
    left: 14px;
  }

  .timeline-vertical .timeline-heading {
    flex-direction: column;
    align-items: flex-start;
  }

  .timeline-vertical .timeline-status {
    margin-top: 0.25rem;
  }

  .sheets-info h3 {
    font-size: 1.1rem;
  }

  .info-list {
    padding: 1rem;
  }

  .info-item {
    padding: 0.5rem 0;
  }

  .info-item .label,
  .info-item .value {
    font-size: 0.85rem;
  }
}

.search-button-container button {
  border-radius: 20px !important;
}

/* Thêm CSS cho phần Kỹ sư triển khai */
.engineer-container {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.engineer-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 0.25rem;
}

.engineer-item .label {
  font-weight: 500;
  color: #555;
}

.engineer-item .value {
  color: #333;
  font-weight: 500;
}

@media (max-width: 768px) {
  .engineer-container {
    flex-direction: column;
    align-items: center;
  }
  
  .engineer-item {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 0.25rem;
  }
}

/* CSS cho phần thông tin nhân sự */
.staff-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #eee;
}

.staff-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 0.25rem;
  width: 100%;
}

.staff-item .label {
  font-weight: 500;
  color: #555;
  font-size: 0.95rem;
}

.staff-item .value {
  color: #333;
  font-weight: 500;
  font-size: 1.05rem;
}

/* Luôn hiển thị theo chiều dọc, kể cả trên màn hình lớn */
@media (min-width: 768px) {
  .staff-container {
    flex-direction: column;
    gap: 1.5rem;
  }
}
/* Tiêu đề truy xuất trạng thái đơn hàng */
.search-title {
  text-align: center;
  margin: 1.5rem 0;
}

.search-title h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #1f61af;
  margin: 0;
  padding: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  display: inline-block;
  line-height: 1.3; /* Thêm line-height để tăng khoảng cách giữa các dòng */
  word-spacing: 2px; /* Tăng khoảng cách giữa các từ */
}

.search-title h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: #1f61af;
  border-radius: 3px;
}

@media (max-width: 768px) {
  .search-title {
    margin: 1rem 0;
    padding: 0 1rem; /* Thêm padding ngang để tránh chạm vào cạnh màn hình */
  }
  
  .search-title h2 {
    font-size: 1.5rem;
    line-height: 1.4; /* Tăng line-height trên mobile */
    word-spacing: 3px; /* Tăng khoảng cách giữa các từ trên mobile */
    word-wrap: break-word; /* Đảm bảo từ dài sẽ được ngắt xuống dòng */
    hyphens: auto; /* Cho phép ngắt từ với dấu gạch nối nếu cần */
  }
}

/* Điều chỉnh thêm cho màn hình rất nhỏ */
@media (max-width: 375px) {
  .search-title h2 {
    font-size: 1.3rem; /* Giảm font size trên màn hình rất nhỏ */
    line-height: 1.5; /* Tăng thêm line-height */
  }
}

/* Cải thiện responsive cho mobile */
@media (max-width: 768px) {
  .search-container {
    padding: 0.75rem;
  }

  .search-form-container {
    padding: 0.75rem;
  }

  /* Tăng kích thước nút điều hướng slider */
  .slider-nav {
    width: 45px;
    height: 45px;
  }

  .slider-nav mat-icon {
    font-size: 24px;
  }

  /* Tăng kích thước nút tìm kiếm */
  .search-button-container button {
    padding: 10px 0;
    font-size: 16px;
  }

  /* Điều chỉnh khoảng cách giữa các phần */
  .order-info {
    margin-bottom: 2rem;
  }

  .staff-container {
    gap: 2rem;
  }

  /* Tăng kích thước font cho tiêu đề đơn hàng */
  .title-value {
    font-size: 1.2rem;
    line-height: 1.4;
  }
}

/* Điều chỉnh cho màn hình rất nhỏ */
@media (max-width: 375px) {
  .search-container {
    padding: 0.5rem;
  }

  /* Giảm padding cho các phần nội dung */
  .order-info {
    padding: 0.75rem;
  }

  /* Điều chỉnh kích thước nút */
  .slider-nav {
    width: 40px;
    height: 40px;
  }
}









