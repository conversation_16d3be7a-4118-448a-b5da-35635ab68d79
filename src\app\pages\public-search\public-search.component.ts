// import { Component, OnInit, ViewChild } from '@angular/core';
// import { MatPaginator } from '@angular/material/paginator';
// import { MatSort } from '@angular/material/sort';
// import { MatTableDataSource } from '@angular/material/table';
// import { RepositoryService } from 'src/app/shared/services/repository.service';
// import { ToastrService } from 'ngx-toastr';
// import { TimelineStep, UnifiedOrderDto } from 'src/app/_interface/unified-order-dto';

// @Component({
//   selector: 'app-public-search',
//   templateUrl: './public-search.component.html',
//   styleUrls: ['./public-search.component.scss']
// })
// export class PublicSearchComponent implements OnInit {
//   displayedColumns: string[] = ['date', 'description', 'status'];
//   public dataSource = new MatTableDataSource<TimelineStep>();
//   searchTerm: string = '';
//   isLoading: boolean = false;
//   orderDetails: UnifiedOrderDto | null = null;

//   @ViewChild(MatPaginator) paginator!: MatPaginator;
//   @ViewChild(MatSort) sort!: MatSort;

//   constructor(
//     private repoService: RepositoryService,
//     private toastr: ToastrService
//   ) { }

//   ngOnInit(): void {
//     // Initial empty state
//   }

//   ngAfterViewInit(): void {
//     this.dataSource.paginator = this.paginator;
//     this.dataSource.sort = this.sort;
//   }

//   search(): void {
//     if (!this.searchTerm.trim()) {
//       this.toastr.info('Vui lòng nhập mã đơn hàng');
//       return;
//     }

//     this.isLoading = true;
//     this.repoService.getData(`api/calendar-report/search-by-order?orderCode=${encodeURIComponent(this.searchTerm)}`)
//       .subscribe({
//         next: (result) => {
//           this.isLoading = false;
          
//           if (!result) {
//             this.toastr.info('Không tìm thấy mã đơn hàng');
//             this.orderDetails = null;
//             return;
//           }
          
//           this.orderDetails = result as UnifiedOrderDto;

//           if (this.orderDetails.source === 'Calendar' && this.orderDetails.timeline) {
//             this.dataSource.data = this.orderDetails.timeline;
//             if (this.orderDetails.timeline.length === 0) {
//               // Không hiển thị thông báo khi không tìm thấy kết quả
//               this.orderDetails = null;
//             }
//           } else if (this.orderDetails.source === 'Sheets') {
//             this.dataSource.data = []; // Không có timeline cho Sheets
//             if (!this.orderDetails.status) {
//               // Không hiển thị thông báo khi không tìm thấy trạng thái
//               this.orderDetails = null;
//             }
//           } else {
//             // Không hiển thị thông báo khi nguồn dữ liệu không xác định
//             this.orderDetails = null;
//           }
//         },
//         error: (err) => {
//           console.error('Lỗi tìm kiếm:', err);
//           // Không hiển thị thông báo lỗi
//           this.isLoading = false;
//           this.orderDetails = null;
//         }
//       });
//   }

//   clearSearch(): void {
//     this.searchTerm = '';
//     this.dataSource.data = [];
//     this.orderDetails = null;
//   }

//   doFilter(value: string): void {
//     this.dataSource.filter = value.trim().toLowerCase();
//   }

//   // Tính phần trăm tiến độ (chỉ áp dụng cho Calendar)
//   getProgressPercentage(): number {
//   if (this.orderDetails?.source !== 'Calendar' || !this.orderDetails?.timeline || this.orderDetails.timeline.length === 0) {
//     return 0;
//   }

//   const totalSteps = this.orderDetails.timeline.length;
//   const completedSteps = this.orderDetails.timeline.filter(item => item.status === 'V').length;

//   if (totalSteps === 0) return 0;

//   return Math.min(100, (completedSteps / totalSteps) * 100);
// }
//   // getProgressPercentage(): number {
//   //   if (this.orderDetails?.source !== 'Calendar' || !this.orderDetails?.timeline || this.orderDetails.timeline.length === 0) return 0;

//   //   const sortedTimeline = [...this.orderDetails.timeline].sort((a, b) => 
//   //     new Date(a.date).getTime() - new Date(b.date).getTime()
//   //   );

//   //   const startDate = new Date(sortedTimeline[0].date);
//   //   const endDate = new Date(sortedTimeline[sortedTimeline.length - 1].date);
//   //   const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
//   //   if (totalDays === 0) return 100;

//   //   const completedItems = sortedTimeline.filter(item => item.status === 'V');
//   //   if (completedItems.length === 0) return 0;

//   //   const sortedCompleted = [...completedItems].sort((a, b) => 
//   //     new Date(b.date).getTime() - new Date(a.date).getTime()
//   //   );

//   //   const lastCompletedDate = new Date(sortedCompleted[0].date);
//   //   const completedDays = Math.ceil((lastCompletedDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

//   //   return Math.min(100, (completedDays / totalDays) * 100);
//   // }

//   // Lớp trạng thái
//   getStatusClass(status: string | null): string {
//     return status === 'V' ? 'status-active' : 'status-inactive';
//   }

//   // Văn bản trạng thái
//   getStatusText(status: string | null): string {
//     return status === 'V' ? 'Hoàn thành' : 'Đang xử lý';
//   }

//   // Kiểm tra đơn hàng trễ hạn (chỉ áp dụng cho Calendar)
//   isOrderDelayed(): boolean {
//     if (this.orderDetails?.source !== 'Calendar' || !this.orderDetails?.endDate || !this.orderDetails?.timeline) return false;
    
//     const plannedEndDate = new Date(this.orderDetails.endDate);
    
//     // Nếu đơn hàng đã hoàn thành, kiểm tra ngày hoàn thành cuối cùng
//     if (this.isOrderCompleted()) {
//       const completedDates = this.orderDetails.timeline
//         .filter(item => item.status === 'V' && item.date)
//         .map(item => new Date(item.date).getTime());
      
//       if (completedDates.length === 0) return false;
      
//       const lastCompletedDate = new Date(Math.max(...completedDates));
//       return lastCompletedDate > plannedEndDate;
//     }
    
//     // Nếu đơn hàng chưa hoàn thành
//     const today = new Date();

//     // Trường hợp 1: Đã quá ngày kết thúc dự kiến và vẫn còn bước chưa hoàn thành
//     if (today > plannedEndDate) {
//       return true;
//     }

//     // Trường hợp 2: Có bước trong timeline có ngày thực hiện vượt quá ngày kết thúc dự kiến
//     return this.orderDetails.timeline.some(item => 
//       new Date(item.date) > plannedEndDate
//     );
//   }

//   // Thông tin trễ hạn (chỉ áp dụng cho Calendar)
//   getDelayInfo(): { days: number, isOverdue: boolean, completedLate: boolean } {
//     if (this.orderDetails?.source !== 'Calendar' || !this.orderDetails?.endDate) 
//       return { days: 0, isOverdue: false, completedLate: false };

//     try {
//       const plannedEndDate = new Date(this.orderDetails.endDate);
//       const today = new Date();
//       let delayDays = 0;
//       let isOverdue = false;
//       let completedLate = false;

//       // Nếu đơn hàng đã hoàn thành
//       if (this.isOrderCompleted() && this.orderDetails.timeline) {
//         const completedDates = this.orderDetails.timeline
//           .filter(item => item.status === 'V' && item.date)
//           .map(item => new Date(item.date).getTime());
        
//         if (completedDates.length > 0) {
//           const lastCompletedDate = new Date(Math.max(...completedDates));
//           if (lastCompletedDate > plannedEndDate) {
//             delayDays = Math.ceil((lastCompletedDate.getTime() - plannedEndDate.getTime()) / (1000 * 60 * 60 * 24));
//             completedLate = true; // Đã hoàn thành nhưng trễ hạn
//           }
//         }
//         return { days: delayDays, isOverdue: false, completedLate };
//       }

//       // Nếu đơn hàng chưa hoàn thành
//       if (today > plannedEndDate) {
//         // Đã quá hạn
//         delayDays = Math.ceil((today.getTime() - plannedEndDate.getTime()) / (1000 * 60 * 60 * 24));
//         isOverdue = true;
//       } else if (this.orderDetails.timeline && this.orderDetails.timeline.length > 0) {
//         // Dự kiến trễ hạn
//         const futureDates = this.orderDetails.timeline
//           .filter(item => item.date && new Date(item.date) > plannedEndDate)
//           .map(item => new Date(item.date).getTime());

//         if (futureDates.length > 0) {
//           const latestDate = new Date(Math.max(...futureDates));
//           delayDays = Math.ceil((latestDate.getTime() - plannedEndDate.getTime()) / (1000 * 60 * 60 * 24));
//         }
//       }

//       return { days: delayDays, isOverdue, completedLate: false };
//     } catch (error) {
//       console.error('Lỗi trong getDelayInfo:', error);
//       return { days: 0, isOverdue: false, completedLate: false };
//     }
//   }

//   // Thông báo trễ hạn
//   getDelayMessage(): string {
//     if (!this.isOrderDelayed()) return '';

//     const delayInfo = this.getDelayInfo();

//     // Nếu đơn hàng đã hoàn thành nhưng trễ hạn

//     if (this.isOrderCompleted() && delayInfo.completedLate) {

//       return `Đơn hàng đã hoàn thành trễ ${delayInfo.days} ngày so với kế hoạch`;

//     }
    
//     // Nếu đơn hàng chưa hoàn thành
//     if (delayInfo.isOverdue) {
      
//       return `Đơn hàng đã quá hạn ${delayInfo.days-1} ngày so với kế hoạch`;
//     } else {
//       return `Dự kiến đơn hàng sẽ trễ ${delayInfo.days} ngày so với kế hoạch`;
//     }
//   }

//   // Kiểm tra chậm tiến độ (chỉ áp dụng cho Calendar)
//   isBehindSchedule(): boolean {
//   if (this.isOrderCompleted()) return false;
  
//   if (this.orderDetails?.source !== 'Calendar' || !this.orderDetails?.timeline || this.orderDetails.timeline.length === 0) return false;

//   const today = new Date();
//   today.setHours(0, 0, 0, 0);

//   // Kiểm tra startDate
//   const startDate = this.orderDetails?.startDate ? new Date(this.orderDetails.startDate) : null;
//   if (startDate) {
//     startDate.setHours(0, 0, 0, 0);
//     // Nếu ngày hiện tại trùng với startDate, không báo chậm
//     if (today.getTime() === startDate.getTime()) {
//       return false;
//     }
//   }

//   try {
//     const sortedTimeline = [...this.orderDetails.timeline].filter(item => item.date).sort((a, b) => {
//       const dateA = new Date(a.date);
//       const dateB = new Date(b.date);
//       return dateA.getTime() - dateB.getTime();
//     });

//     // Chỉ kiểm tra các bước có ngày nhỏ hơn today (không bao gồm hôm nay)
//     const itemsDueBeforeToday = sortedTimeline.filter(item => {
//       if (!item.date) return false;
//       const itemDate = new Date(item.date);
//       itemDate.setHours(0, 0, 0, 0);
//       return itemDate < today;
//     });

//     if (itemsDueBeforeToday.length === 0) return false;
//     return itemsDueBeforeToday.some(item => item.status !== 'V');
//   } catch (error) {
//     return false;
//   }
// }

//   // Thông tin chậm tiến độ
//   getBehindScheduleInfo(): { daysLate: number, itemsLate: number } {
//   if (this.isOrderCompleted() || !this.isBehindSchedule()) 
//     return { daysLate: 0, itemsLate: 0 };
  
//   if (this.orderDetails?.source !== 'Calendar' || !this.orderDetails?.timeline || this.orderDetails.timeline.length === 0) 
//     return { daysLate: 0, itemsLate: 0 };

//   try {
//     const today = new Date();
//     today.setHours(0, 0, 0, 0);

//     const sortedTimeline = [...this.orderDetails.timeline].filter(item => item.date).sort((a, b) => {
//       const dateA = new Date(a.date);
//       const dateB = new Date(b.date);
//       return dateA.getTime() - dateB.getTime();
//     });

//     // Chỉ tính các bước có ngày nhỏ hơn today
//     const lateItems = sortedTimeline.filter(item => {
//       if (!item.date) return false;
//       const itemDate = new Date(item.date);
//       itemDate.setHours(0, 0, 0, 0);
//       return itemDate < today && item.status !== 'V';
//     });

//     const itemsLate = lateItems.length;
//     if (itemsLate === 0) return { daysLate: 0, itemsLate: 0 };

//     const oldestLateItem = lateItems.sort((a, b) => {
//       const dateA = new Date(a.date);
//       const dateB = new Date(b.date);
//       return dateA.getTime() - dateB.getTime();
//     })[0];

//     const oldestDate = new Date(oldestLateItem.date);
//     oldestDate.setHours(0, 0, 0, 0);
//     const daysLate = Math.floor((today.getTime() - oldestDate.getTime()) / (1000 * 60 * 60 * 24));

//     return { daysLate, itemsLate };
//   } catch (error) {
//     return { daysLate: 0, itemsLate: 0 };
//   }
// }

//   // Thông báo chậm tiến độ
//   getBehindScheduleMessage(): string {
//   if (!this.isBehindSchedule()) {
//     // Nếu không chậm, trả về trạng thái đang tiến hành
//     return this.isOrderCompleted() ? '' : 'Đơn hàng đang tiến hành';
//   }

//   try {
//     const { daysLate, itemsLate } = this.getBehindScheduleInfo();
//     if (itemsLate === 1) {
//       return `Đơn hàng đang chậm 1 công đoạn (${daysLate} ngày) so với kế hoạch`;
//     } else {
//       return `Đơn hàng đang chậm ${itemsLate} công đoạn (${daysLate} ngày) so với kế hoạch`;
//     }
//   } catch (error) {
//     return 'Đơn hàng đang chậm tiến độ so với kế hoạch';
//   }
// }

//   // Trạng thái tiến độ
//   getProgressStatus(): string {
//     if (!this.orderDetails) return 'Chưa có dữ liệu';

//     if (this.orderDetails.source === 'Sheets') {
//       return this.orderDetails.status || 'Không xác định';
//     }

//     const percentage = this.getProgressPercentage();
    
//     // Nếu đơn hàng đã hoàn thành
//     if (this.isOrderCompleted()) {
//       const delayInfo = this.getDelayInfo();
//       if (delayInfo.completedLate) {
//         return `Đã hoàn thành (trễ ${delayInfo.days} ngày)`;
//       } else {
//         return 'Đã hoàn thành đúng hạn';
//       }
//     }
    
//     // Nếu đơn hàng chưa hoàn thành
//     if (this.isOrderDelayed()) {
//       const delayInfo = this.getDelayInfo();
//       if (delayInfo.isOverdue) {
//         return `Đã quá hạn - Hoàn thành ${percentage.toFixed(0)}%`;
//       } else {
//         return `Có nguy cơ trễ hạn - Hoàn thành ${percentage.toFixed(0)}%`;
//       }
//     } else if (this.isBehindSchedule()) {
//       return `Chậm tiến độ - Hoàn thành ${percentage.toFixed(0)}%`;
//     } else {
//       return `Đang tiến hành - Hoàn thành ${percentage.toFixed(0)}%`;
//     }
//   }

//   // Kiểm tra xem đơn hàng đã hoàn thành chưa
//   isOrderCompleted(): boolean {
//     if (this.orderDetails?.source !== 'Calendar' || !this.orderDetails?.timeline || this.orderDetails.timeline.length === 0) 
//       return false;
    
//     // Đơn hàng được coi là hoàn thành khi tất cả các bước trong timeline đều có status là 'V'
//     return this.orderDetails.timeline.every(item => item.status === 'V');
//   }
// }

import { Component, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { RepositoryService } from 'src/app/shared/services/repository.service';
import { ToastrService } from 'ngx-toastr';
import { TimelineStep, UnifiedOrderDto } from 'src/app/_interface/unified-order-dto';
import { ActivatedRoute, Router } from '@angular/router';

export interface SlideData {
  image: string;
  link: string;
  alt: string;
}

@Component({
  selector: 'app-public-search',
  templateUrl: './public-search.component.html',
  styleUrls: ['./public-search.component.scss']
})
export class PublicSearchComponent implements OnInit, OnDestroy {
  displayedColumns: string[] = ['date', 'description', 'status'];
  public dataSource = new MatTableDataSource<TimelineStep>();
  searchTerm: string = '';
  isLoading: boolean = false;
  orderDetails: UnifiedOrderDto | null = null;

  // Slider properties
  slides: SlideData[] = [
    {
      image: 'https://atpro.com.vn/wp-content/uploads/2025/04/banner-may-tinh-cong-nghiep-29-04-1024x346.jpg',
      link: 'https://atpro.com.vn/may-tinh-cong-nghiep/',
      alt: 'máy tính công nghiệp'
    },
    {
      image: 'https://atpro.com.vn/wp-content/uploads/2025/01/he-thong-xep-hang-tu-dong-AT-QMS05-5.jpg',
      link: 'https://atpro.com.vn/san-pham/he-thong-xep-hang-tu-dong-cao-cap/',
      alt: 'Hệ thống xếp hàng tự động – AT-QMS05'
    },
    {
      image: 'https://atpro.com.vn/wp-content/uploads/2025/04/dong-ho-led-treo-tuong.jpg',
      link: 'https://atpro.com.vn/danh-muc/dong-ho-dien-tu/',
      alt: 'Đồng hồ led treo tường'
    },
    {
      image: 'https://atpro.com.vn/wp-content/uploads/2024/10/den-thap.jpg',
      link: 'https://atpro.com.vn/danh-muc/den-bao/den-thap/',
      alt: 'Đèn tháp'
    }
  ];

  currentSlide: number = 0;
  autoSlideInterval: any;
  autoSlideDelay: number = 5000; // 5 seconds

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private repoService: RepositoryService,
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.startAutoSlide();
    
    // Check for order parameter in URL and perform search immediately
    this.route.queryParams.subscribe(params => {
      let orderParam = params['order'];
      
      // Kiểm tra xem có phải URL từ Gmail không (có thể bị mã hóa)
      if (!orderParam && window.location.href.includes('order%3D')) {
        // Trích xuất mã đơn hàng từ URL bị mã hóa
        const urlString = window.location.href;
        const orderMatch = urlString.match(/order%3D([^&]+)/);
        if (orderMatch && orderMatch[1]) {
          orderParam = decodeURIComponent(orderMatch[1]);
        }
      }
      
      if (orderParam) {
        this.searchTerm = orderParam;
        // Sử dụng setTimeout với độ trễ 100ms để đảm bảo component đã render
        setTimeout(() => {
          this.search();
        }, 100);
      }
    });
  }

  ngOnDestroy(): void {
    this.stopAutoSlide();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  // ================ SLIDER METHODS ================
  startAutoSlide(): void {
    this.autoSlideInterval = setInterval(() => {
      this.nextSlide();
    }, this.autoSlideDelay);
  }

  stopAutoSlide(): void {
    if (this.autoSlideInterval) {
      clearInterval(this.autoSlideInterval);
    }
  }

  nextSlide(): void {
    if (this.currentSlide < this.slides.length - 1) {
      this.currentSlide++;
    } else {
      this.currentSlide = 0; // Loop back to first slide
    }
    this.resetAutoSlide();
  }

  previousSlide(): void {
    if (this.currentSlide > 0) {
      this.currentSlide--;
    } else {
      this.currentSlide = this.slides.length - 1; // Loop to last slide
    }
    this.resetAutoSlide();
  }

  goToSlide(index: number): void {
    this.currentSlide = index;
    this.resetAutoSlide();
  }

  private resetAutoSlide(): void {
    this.stopAutoSlide();
    this.startAutoSlide();
  }

  // Pause auto-slide on hover
  onSliderMouseEnter(): void {
    this.stopAutoSlide();
  }

  onSliderMouseLeave(): void {
    this.startAutoSlide();
  }

  // ================ EXISTING SEARCH METHODS ================
  // Update URL when searching manually
  updateUrlWithOrderCode(): void {
    if (this.searchTerm) {
      this.router.navigate([], {
        relativeTo: this.route,
        queryParams: { order: this.searchTerm },
        queryParamsHandling: 'merge'
      });
    } else {
      // Remove query parameter if search term is empty
      this.router.navigate([], {
        relativeTo: this.route,
        queryParams: { order: null },
        queryParamsHandling: 'merge'
      });
    }
  }

  // Cập nhật phương thức search() trong component
  search(): void {
  if (!this.searchTerm.trim()) {
    this.toastr.info('Vui lòng nhập mã đơn hàng');
    return;
  }

  // Update URL with order code
  this.updateUrlWithOrderCode();

  // Bật loading
  this.isLoading = true;
  
  this.repoService.getDataWithoutLoading(`api/calendar-report/search-by-order?orderCode=${encodeURIComponent(this.searchTerm)}`)
    .subscribe({
      next: (result) => {
        // Tắt loading
        this.isLoading = false;
        
        if (!result) {
          this.toastr.info('Không tìm thấy mã đơn hàng');
          this.orderDetails = null;
          return;
        }
        
        this.orderDetails = result as UnifiedOrderDto;

        if (this.orderDetails.source === 'Calendar' && this.orderDetails.timeline) {
          this.dataSource.data = this.orderDetails.timeline;
          if (this.orderDetails.timeline.length === 0) {
            // Không hiển thị thông báo khi không tìm thấy kết quả
          }
        } else if (this.orderDetails.source === 'Sheets') {
          this.dataSource.data = []; // Không có timeline cho Sheets
          if (!this.orderDetails.status) {
            // Không hiển thị thông báo khi không tìm thấy trạng thái
          }
        } else {
          // Không hiển thị thông báo khi nguồn dữ liệu không xác định
          this.orderDetails = null;
        }
      },
      error: (err) => {
        console.error('Lỗi tìm kiếm:', err);
        // Tắt loading khi có lỗi
        this.isLoading = false;
        this.orderDetails = null;
      }
    });
}

  clearSearch(): void {
    this.searchTerm = '';
    this.dataSource.data = [];
    this.orderDetails = null;
    
    // Clear URL parameter
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { order: null },
      queryParamsHandling: 'merge'
    });
  }

  doFilter(value: string): void {
    this.dataSource.filter = value.trim().toLowerCase();
  }

  // ================ PROGRESS & STATUS METHODS ================
  getProgressPercentage(): number {
    if (this.orderDetails?.source !== 'Calendar' || !this.orderDetails?.timeline || this.orderDetails.timeline.length === 0) {
      return 0;
    }

    const totalSteps = this.orderDetails.timeline.length;
    const completedSteps = this.orderDetails.timeline.filter(item => item.status === 'V').length;

    if (totalSteps === 0) return 0;

    return Math.min(100, (completedSteps / totalSteps) * 100);
  }

  getStatusClass(status: string | null): string {
    return status === 'V' ? 'status-active' : 'status-inactive';
  }

  getStatusText(status: string | null): string {
    return status === 'V' ? 'Hoàn thành' : 'Đang xử lý';
  }

  isOrderDelayed(): boolean {
    if (this.orderDetails?.source !== 'Calendar' || !this.orderDetails?.endDate || !this.orderDetails?.timeline) return false;
    
    const plannedEndDate = new Date(this.orderDetails.endDate);
    
    // Nếu đơn hàng đã hoàn thành, kiểm tra ngày hoàn thành cuối cùng
    if (this.isOrderCompleted()) {
      const completedDates = this.orderDetails.timeline
        .filter(item => item.status === 'V' && item.date)
        .map(item => new Date(item.date).getTime());
      
      if (completedDates.length === 0) return false;
      
      const lastCompletedDate = new Date(Math.max(...completedDates));
      return lastCompletedDate > plannedEndDate;
    }
    
    // Nếu đơn hàng chưa hoàn thành
    const today = new Date();

    // Trường hợp 1: Đã quá ngày kết thúc dự kiến và vẫn còn bước chưa hoàn thành
    if (today > plannedEndDate) {
      return true;
    }

    // Trường hợp 2: Có bước trong timeline có ngày thực hiện vượt quá ngày kết thúc dự kiến
    return this.orderDetails.timeline.some(item => 
      new Date(item.date) > plannedEndDate
    );
  }

  getDelayInfo(): { days: number, isOverdue: boolean, completedLate: boolean } {
    if (this.orderDetails?.source !== 'Calendar' || !this.orderDetails?.endDate) 
      return { days: 0, isOverdue: false, completedLate: false };

    try {
      const plannedEndDate = new Date(this.orderDetails.endDate);
      const today = new Date();
      let delayDays = 0;
      let isOverdue = false;
      let completedLate = false;

      // Nếu đơn hàng đã hoàn thành
      if (this.isOrderCompleted() && this.orderDetails.timeline) {
        const completedDates = this.orderDetails.timeline
          .filter(item => item.status === 'V' && item.date)
          .map(item => new Date(item.date).getTime());
        
        if (completedDates.length > 0) {
          const lastCompletedDate = new Date(Math.max(...completedDates));
          if (lastCompletedDate > plannedEndDate) {
            delayDays = Math.ceil((lastCompletedDate.getTime() - plannedEndDate.getTime()) / (1000 * 60 * 60 * 24));
            completedLate = true;
          }
        }
        return { days: delayDays, isOverdue: false, completedLate };
      }

      // Nếu đơn hàng chưa hoàn thành
      if (today > plannedEndDate) {
        // Đã quá hạn
        delayDays = Math.ceil((today.getTime() - plannedEndDate.getTime()) / (1000 * 60 * 60 * 24));
        isOverdue = true;
      } else if (this.orderDetails.timeline && this.orderDetails.timeline.length > 0) {
        // Dự kiến trễ hạn
        const futureDates = this.orderDetails.timeline
          .filter(item => item.date && new Date(item.date) > plannedEndDate)
          .map(item => new Date(item.date).getTime());

        if (futureDates.length > 0) {
          const latestDate = new Date(Math.max(...futureDates));
          delayDays = Math.ceil((latestDate.getTime() - plannedEndDate.getTime()) / (1000 * 60 * 60 * 24));
        }
      }

      return { days: delayDays, isOverdue, completedLate: false };
    } catch (error) {
      console.error('Lỗi trong getDelayInfo:', error);
      return { days: 0, isOverdue: false, completedLate: false };
    }
  }

  getDelayMessage(): string {
    if (!this.isOrderDelayed()) return '';

    const delayInfo = this.getDelayInfo();

    // Nếu đơn hàng đã hoàn thành nhưng trễ hạn
    if (this.isOrderCompleted() && delayInfo.completedLate) {
      return `Đơn hàng đã hoàn thành trễ ${delayInfo.days} ngày so với kế hoạch`;
    }
    
    // Nếu đơn hàng chưa hoàn thành
    if (delayInfo.isOverdue) {
      return `Đơn hàng đã quá hạn ${delayInfo.days-1} ngày so với kế hoạch`;
    } else {
      return `Dự kiến đơn hàng sẽ trễ ${delayInfo.days} ngày so với kế hoạch`;
    }
  }

  isBehindSchedule(): boolean {
    if (this.isOrderCompleted()) return false;
    
    if (this.orderDetails?.source !== 'Calendar' || !this.orderDetails?.timeline || this.orderDetails.timeline.length === 0) return false;

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Kiểm tra startDate
    const startDate = this.orderDetails?.startDate ? new Date(this.orderDetails.startDate) : null;
    if (startDate) {
      startDate.setHours(0, 0, 0, 0);
      // Nếu ngày hiện tại trùng với startDate, không báo chậm
      if (today.getTime() === startDate.getTime()) {
        return false;
      }
    }

    try {
      const sortedTimeline = [...this.orderDetails.timeline].filter(item => item.date).sort((a, b) => {
        const dateA = new Date(a.date);
        const dateB = new Date(b.date);
        return dateA.getTime() - dateB.getTime();
      });

      // Chỉ kiểm tra các bước có ngày nhỏ hơn today (không bao gồm hôm nay)
      const itemsDueBeforeToday = sortedTimeline.filter(item => {
        if (!item.date) return false;
        const itemDate = new Date(item.date);
        itemDate.setHours(0, 0, 0, 0);
        return itemDate < today;
      });

      if (itemsDueBeforeToday.length === 0) return false;
      return itemsDueBeforeToday.some(item => item.status !== 'V');
    } catch (error) {
      return false;
    }
  }

  getBehindScheduleInfo(): { daysLate: number, itemsLate: number } {
    if (this.isOrderCompleted() || !this.isBehindSchedule()) 
      return { daysLate: 0, itemsLate: 0 };
    
    if (this.orderDetails?.source !== 'Calendar' || !this.orderDetails?.timeline || this.orderDetails.timeline.length === 0) 
      return { daysLate: 0, itemsLate: 0 };

    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const sortedTimeline = [...this.orderDetails.timeline].filter(item => item.date).sort((a, b) => {
        const dateA = new Date(a.date);
        const dateB = new Date(b.date);
        return dateA.getTime() - dateB.getTime();
      });

      // Chỉ tính các bước có ngày nhỏ hơn today
      const lateItems = sortedTimeline.filter(item => {
        if (!item.date) return false;
        const itemDate = new Date(item.date);
        itemDate.setHours(0, 0, 0, 0);
        return itemDate < today && item.status !== 'V';
      });

      const itemsLate = lateItems.length;
      if (itemsLate === 0) return { daysLate: 0, itemsLate: 0 };

      const oldestLateItem = lateItems.sort((a, b) => {
        const dateA = new Date(a.date);
        const dateB = new Date(b.date);
        return dateA.getTime() - dateB.getTime();
      })[0];

      const oldestDate = new Date(oldestLateItem.date);
      oldestDate.setHours(0, 0, 0, 0);
      const daysLate = Math.floor((today.getTime() - oldestDate.getTime()) / (1000 * 60 * 60 * 24));

      return { daysLate, itemsLate };
    } catch (error) {
      return { daysLate: 0, itemsLate: 0 };
    }
  }

  getBehindScheduleMessage(): string {
    if (!this.isBehindSchedule()) {
      // Nếu không chậm, trả về trạng thái đang tiến hành
      return this.isOrderCompleted() ? '' : 'Đơn hàng đang tiến hành';
    }

    try {
      const { daysLate, itemsLate } = this.getBehindScheduleInfo();
      if (itemsLate === 1) {
        return `Đơn hàng đang chậm 1 công đoạn (${daysLate} ngày) so với kế hoạch`;
      } else {
        return `Đơn hàng đang chậm ${itemsLate} công đoạn (${daysLate} ngày) so với kế hoạch`;
      }
    } catch (error) {
      return 'Đơn hàng đang chậm tiến độ so với kế hoạch';
    }
  }

  getProgressStatus(): string {
    if (!this.orderDetails) return 'Chưa có dữ liệu';

    if (this.orderDetails.source === 'Sheets') {
      return this.orderDetails.status || 'Không xác định';
    }

    const percentage = this.getProgressPercentage();
    
    // Nếu đơn hàng đã hoàn thành
    if (this.isOrderCompleted()) {
      const delayInfo = this.getDelayInfo();
      if (delayInfo.completedLate) {
        return `Đã hoàn thành (trễ ${delayInfo.days} ngày)`;
      } else {
        return 'Đã hoàn thành đúng hạn';
      }
    }
    
    // Nếu đơn hàng chưa hoàn thành
    if (this.isOrderDelayed()) {
      const delayInfo = this.getDelayInfo();
      if (delayInfo.isOverdue) {
        return `Đã quá hạn - Hoàn thành ${percentage.toFixed(0)}%`;
      } else {
        return `Có nguy cơ trễ hạn - Hoàn thành ${percentage.toFixed(0)}%`;
      }
    } else if (this.isBehindSchedule()) {
      return `Chậm tiến độ - Hoàn thành ${percentage.toFixed(0)}%`;
    } else {
      return `Đang tiến hành - Hoàn thành ${percentage.toFixed(0)}%`;
    }
  }

  isOrderCompleted(): boolean {
    if (this.orderDetails?.source !== 'Calendar' || !this.orderDetails?.timeline || this.orderDetails.timeline.length === 0) 
      return false;
    
    // Đơn hàng được coi là hoàn thành khi tất cả các bước trong timeline đều có status là 'V'
    return this.orderDetails.timeline.every(item => item.status === 'V');
  }
}



