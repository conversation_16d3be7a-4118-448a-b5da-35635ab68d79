import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MaterialModule } from '../../material.module';
import { PublicSearchComponent } from './public-search.component';

@NgModule({
  declarations: [
    PublicSearchComponent
  ],
  imports: [
    CommonModule,
    MaterialModule,
    FormsModule,
    RouterModule.forChild([
      {
        path: '',
        component: PublicSearchComponent
      }
    ])
  ]
})
export class PublicSearchModule { }