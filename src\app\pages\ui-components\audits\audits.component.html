<mat-card class="cardWithShadow theme-card">
  <mat-card-header>
    <mat-card-title class="m-b-0">Audit logs</mat-card-title>
    <span class="flex-1-auto"></span>

    <button mat-flat-button color="accent" matTooltipPosition="left" class="m-l-8" matTooltipHideDelay="100000"
      (click)="refresh()">
      <span fxHide.xs>Refresh</span> <mat-icon>refresh</mat-icon>
    </button>
  </mat-card-header>

  <mat-card-content class="b-t-1">
    <div class="table-responsive m-t-16">
      <table mat-table [dataSource]="dataSource" class="w-100" matSort>
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14" mat-sort-header>
            Entity
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            {{ element.entityName }}
          </td>
        </ng-container>

        <ng-container matColumnDef="action">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14">
            Action
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            {{ element.action }}
          </td>
        </ng-container>
        <ng-container matColumnDef="date">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14" mat-sort-header>
            Creation time
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            {{ element.timestamp | date : "medium" }}
          </td>
        </ng-container>

        <ng-container matColumnDef="change">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14">
            Changes
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            {{ element.changes }}
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
    </div>

    <mat-paginator [pageSize]="4" [pageSizeOptions]="[4, 6, 10, 20]">
    </mat-paginator>
  </mat-card-content>
</mat-card>