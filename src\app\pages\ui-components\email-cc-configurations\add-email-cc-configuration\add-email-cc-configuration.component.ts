// src/app/pages/ui-components/email-cc-configurations/add-email-cc-configuration/add-email-cc-configuration.component.ts

import { Component, OnInit } from '@angular/core';
import { FormGroup, FormControl, Validators, FormArray } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { EmailCcConfigurationForCreationDto } from 'src/app/_interface/email-cc-configuration';
import { DataService } from 'src/app/shared/services/data.service';
import { DialogService } from 'src/app/shared/services/dialog.service';
import { RepositoryService } from 'src/app/shared/services/repository.service';

@Component({
  selector: 'app-add-email-cc-configuration',
  templateUrl: './add-email-cc-configuration.component.html',
})
export class AddEmailCcConfigurationComponent implements OnInit {
  dataForm!: FormGroup;

  constructor( 
    private repoService: RepositoryService,
    private toastr: ToastrService,
    private dataService: DataService,
    private dialogService: DialogService,
    private dialogRef: MatDialogRef<AddEmailCcConfigurationComponent>
  ) {}

  ngOnInit() {
    this.dataForm = new FormGroup({
      configKey: new FormControl('', [Validators.required, Validators.maxLength(100)]),
      configName: new FormControl('', [Validators.required, Validators.maxLength(200)]),
      isEnabled: new FormControl(true),
      description: new FormControl('', [Validators.maxLength(500)]),
      defaultCcEmails: new FormArray([]),
      defaultBccEmails: new FormArray([]),
      autoAddCcForOrders: new FormControl(false),
      autoAddCcForNotifications: new FormControl(false),
      autoAddCcForAlerts: new FormControl(false),
      priority: new FormControl(1, [Validators.required, Validators.min(1)])
    });

    // Add initial email controls
    this.addCcEmail();
    this.addBccEmail();
  }

  get defaultCcEmails(): FormArray {
    return this.dataForm.get('defaultCcEmails') as FormArray;
  }

  get defaultBccEmails(): FormArray {
    return this.dataForm.get('defaultBccEmails') as FormArray;
  }

  addCcEmail() {
    const emailControl = new FormControl('', [Validators.email]);
    this.defaultCcEmails.push(emailControl);
  }

  removeCcEmail(index: number) {
    if (this.defaultCcEmails.length > 1) {
      this.defaultCcEmails.removeAt(index);
    }
  }

  addBccEmail() {
    const emailControl = new FormControl('', [Validators.email]);
    this.defaultBccEmails.push(emailControl);
  }

  removeBccEmail(index: number) {
    if (this.defaultBccEmails.length > 1) {
      this.defaultBccEmails.removeAt(index);
    }
  }

  public validateControl = (controlName: string) => {
    return this.dataForm?.get(controlName)?.invalid && this.dataForm?.get(controlName)?.touched
  }

  public hasError = (controlName: string, errorName: string) => {
    return this.dataForm?.get(controlName)?.hasError(errorName)
  }

  public validateEmailControl = (formArray: FormArray, index: number) => {
    const control = formArray.at(index);
    return control?.invalid && control?.touched;
  }

  public hasEmailError = (formArray: FormArray, index: number, errorName: string) => {
    const control = formArray.at(index);
    return control?.hasError(errorName);
  }

  public createData = (dataFormValue: any) => {
    if (this.dataForm.valid) {
      this.executeDataCreation(dataFormValue);
    }
  };

  private executeDataCreation = (dataFormValue: any) => {
    // Filter out empty emails
    const ccEmails = dataFormValue.defaultCcEmails.filter((email: string) => email && email.trim() !== '');
    const bccEmails = dataFormValue.defaultBccEmails.filter((email: string) => email && email.trim() !== '');

    const data: EmailCcConfigurationForCreationDto = {
      configKey: dataFormValue.configKey,
      configName: dataFormValue.configName,
      isEnabled: dataFormValue.isEnabled,
      description: dataFormValue.description,
      defaultCcEmails: ccEmails,
      defaultBccEmails: bccEmails,
      autoAddCcForOrders: dataFormValue.autoAddCcForOrders,
      autoAddCcForNotifications: dataFormValue.autoAddCcForNotifications,
      autoAddCcForAlerts: dataFormValue.autoAddCcForAlerts,
      priority: dataFormValue.priority
    };

    const apiUri: string = `api/email-cc-configuration`;
    this.repoService.create(apiUri, data).subscribe(
      (res) => {
        this.dialogService.openSuccessDialog("The email CC configuration has been created successfully.")
          .afterClosed()
          .subscribe(() => {
            this.dataService.triggerRefreshTab1();
            this.closeModal();
          });
      },
      (error) => {
        this.toastr.error(error.error?.detail || "Error creating configuration");
      }
    );
  };

  closeModal() {
    this.dialogRef.close();
  }
}