<mat-card class="cardWithShadow theme-card">
  <mat-card-header>
    <mat-card-title class="m-b-0">Email CC Configurations</mat-card-title>
    <span class="flex-1-auto"></span>
    <button
      mat-flat-button
      color="primary"
      matTooltipPosition="left"
      class="m-l-8"
      matTooltipHideDelay="100000"
      (click)="addEmailCcConfiguration()"
    >
      <mat-icon>add</mat-icon>Create New Configuration
    </button>
  </mat-card-header>

  <mat-card-content class="b-t-1">
    <!-- Search Filter -->
    <div class="m-b-16">
      <mat-form-field class="w-100" appearance="outline">
        <mat-label>Search configurations...</mat-label>
        <input matInput (keyup)="doFilter($any($event.target).value)" placeholder="Filter by config key, name, or description">
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
    </div>

    <div class="table-responsive m-t-16">
      <table mat-table [dataSource]="dataSource" class="w-100" matSort>
        
        <!-- Action Column -->
        <ng-container matColumnDef="action">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14">
            Action
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            <button
              mat-flat-button
              color="primary"
              [matMenuTriggerFor]="actions"
              class="m-t-8"
            >
              Action <mat-icon>arrow_drop_down</mat-icon>
            </button>
            <mat-menu class="cardWithShadow" #actions="matMenu">
              <button mat-menu-item (click)="updateEmailCcConfiguration(element.id)">
                <mat-icon>edit</mat-icon> Edit
              </button>
              <button mat-menu-item (click)="toggleConfiguration(element.id)">
                <mat-icon>{{element.isEnabled ? 'toggle_off' : 'toggle_on'}}</mat-icon>
                {{element.isEnabled ? 'Disable' : 'Enable'}}
              </button>
              <button mat-menu-item (click)="deleteEmailCcConfiguration(element.id)" class="text-red">
                <mat-icon color="warn">delete</mat-icon> Delete
              </button>
            </mat-menu>
          </td>
        </ng-container>

        <!-- Config Key Column -->
        <ng-container matColumnDef="configKey">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="f-w-600 mat-subtitle-1 f-s-14">
            Config Key
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            <span class="badge badge-outline-primary">{{ element.configKey }}</span>
          </td>
        </ng-container>

        <!-- Config Name Column -->
        <ng-container matColumnDef="configName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="f-w-600 mat-subtitle-1 f-s-14">
            Config Name
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            {{ element.configName }}
          </td>
        </ng-container>

        <!-- Is Enabled Column -->
        <ng-container matColumnDef="isEnabled">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="f-w-600 mat-subtitle-1 f-s-14">
            Status
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            <span class="badge" [ngClass]="element.isEnabled ? 'badge-light-success' : 'badge-light-error'">
              {{ element.isEnabled ? 'Enabled' : 'Disabled' }}
            </span>
          </td>
        </ng-container>

        <!-- Description Column -->
        <ng-container matColumnDef="description">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14">
            Description
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            {{ element.description || 'No description' }}
          </td>
        </ng-container>

        <!-- Default CC Emails Column -->
        <ng-container matColumnDef="defaultCcEmails">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14">
            CC Emails
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            <span class="text-truncate" [matTooltip]="formatEmails(element.defaultCcEmails)">
              {{ formatEmails(element.defaultCcEmails) }}
            </span>
          </td>
        </ng-container>

        <!-- Auto Add CC For Orders Column -->
        <ng-container matColumnDef="autoAddCcForOrders">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14">
            Orders
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            <mat-icon [color]="element.autoAddCcForOrders ? 'primary' : 'disabled'">
              {{ element.autoAddCcForOrders ? 'check_circle' : 'cancel' }}
            </mat-icon>
          </td>
        </ng-container>

        <!-- Auto Add CC For Notifications Column -->
        <ng-container matColumnDef="autoAddCcForNotifications">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14">
            Notifications
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            <mat-icon [color]="element.autoAddCcForNotifications ? 'primary' : 'disabled'">
              {{ element.autoAddCcForNotifications ? 'check_circle' : 'cancel' }}
            </mat-icon>
          </td>
        </ng-container>

        <!-- Auto Add CC For Alerts Column -->
        <ng-container matColumnDef="autoAddCcForAlerts">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14">
            Alerts
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            <mat-icon [color]="element.autoAddCcForAlerts ? 'primary' : 'disabled'">
              {{ element.autoAddCcForAlerts ? 'check_circle' : 'cancel' }}
            </mat-icon>
          </td>
        </ng-container>

        <!-- Priority Column -->
        <ng-container matColumnDef="priority">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="f-w-600 mat-subtitle-1 f-s-14">
            Priority
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            <span class="badge badge-outline-info">{{ element.priority }}</span>
          </td>
        </ng-container>

        <!-- Created At Column -->
        <ng-container matColumnDef="createdAt">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="f-w-600 mat-subtitle-1 f-s-14">
            Created At
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            {{ element.createdAt | date:'short' }}
          </td>
        </ng-container>
        
        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>

      <!-- Paginator -->
      <mat-paginator 
        [pageSizeOptions]="[5, 10, 25, 100]" 
        showFirstLastButtons>
      </mat-paginator>
    </div>
  </mat-card-content>
</mat-card>