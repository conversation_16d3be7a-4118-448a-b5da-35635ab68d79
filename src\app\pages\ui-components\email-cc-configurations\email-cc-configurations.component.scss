// src/app/pages/ui-components/email-cc-configurations/email-cc-configurations.component.scss

.text-truncate {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-red {
  color: #f44336;
}

.badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  
  &.badge-outline-primary {
    border: 1px solid #5d87ff;
    color: #5d87ff;
    background: transparent;
  }
  
  &.badge-outline-info {
    border: 1px solid #13deb9;
    color: #13deb9;
    background: transparent;
  }
  
  &.badge-light-success {
    background: rgba(19, 222, 185, 0.1);
    color: #13deb9;
  }
  
  &.badge-light-error {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
  }
}

// src/app/pages/ui-components/email-cc-configurations/add-email-cc-configuration/add-email-cc-configuration.component.scss

.modal-card {
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.row {
  margin-left: -8px;
  margin-right: -8px;
  
  .col-md-4,
  .col-md-6,
  .col-10,
  .col-2 {
    padding-left: 8px;
    padding-right: 8px;
  }
}

h6 {
  font-weight: 600;
  margin-bottom: 12px;
  color: #2a3547;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
}

.text-right {
  text-align: right;
}

.align-items-center {
  display: flex;
  align-items: center;
}

// src/app/pages/ui-components/email-cc-configurations/update-email-cc-configuration/update-email-cc-configuration.component.scss

.modal-card {
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.row {
  margin-left: -8px;
  margin-right: -8px;
  
  .col-md-4,
  .col-md-6,
  .col-10,
  .col-2 {
    padding-left: 8px;
    padding-right: 8px;
  }
}

h6 {
  font-weight: 600;
  margin-bottom: 12px;
  color: #2a3547;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
}

.text-right {
  text-align: right;
}

.align-items-center {
  display: flex;
  align-items: center;
}