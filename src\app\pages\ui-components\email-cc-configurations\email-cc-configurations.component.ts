// src/app/pages/ui-components/email-cc-configurations/email-cc-configurations.component.ts

import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { Subscription } from 'rxjs';
import { EmailCcConfigurationDto } from 'src/app/_interface/email-cc-configuration';
import { RepositoryService } from 'src/app/shared/services/repository.service';
import { DialogService } from 'src/app/shared/services/dialog.service';
import { DataService } from 'src/app/shared/services/data.service';
import { AddEmailCcConfigurationComponent } from './add-email-cc-configuration/add-email-cc-configuration.component';
import { UpdateEmailCcConfigurationComponent } from './update-email-cc-configuration/update-email-cc-configuration.component';

@Component({
  selector: 'app-email-cc-configurations',
  templateUrl: './email-cc-configurations.component.html',
  styleUrls: ['./email-cc-configurations.component.scss']
})
export class EmailCcConfigurationsComponent implements OnInit {
  displayedColumns: string[] = [
    'action', 
    'configKey', 
    'configName', 
    'isEnabled', 
    'description',
    'defaultCcEmails',
    // 'autoAddCcForOrders',
    // 'autoAddCcForNotifications', 
    // 'autoAddCcForAlerts',
    // 'priority',
    // 'createdAt'
  ];
  
  public dataSource = new MatTableDataSource<EmailCcConfigurationDto>();
  private refreshSubscription!: Subscription;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(    
    private repoService: RepositoryService,
    private dialog: MatDialog,
    private dataService: DataService,
    private dialogService: DialogService
  ) { 
    this.refreshSubscription = this.dataService.refreshTab1$.subscribe(() => {
      this.getEmailCcConfigurations();
    });
  }

  ngOnInit(): void {
    this.getEmailCcConfigurations();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  ngOnDestroy(): void {
    if (this.refreshSubscription) {
      this.refreshSubscription.unsubscribe();
    }
  }

  public getEmailCcConfigurations() {
    this.repoService.getData('api/email-cc-configuration')
      .subscribe(res => {
        this.dataSource.data = res as EmailCcConfigurationDto[];
      },
      (err) => {
        console.log(err);
      })
  }

  addEmailCcConfiguration() {
    const popup = this.dialog.open(AddEmailCcConfigurationComponent, {
      width: '800px', 
      height: '600px',
      enterAnimationDuration: '100ms',
      exitAnimationDuration: '100ms',
    });
  }

  updateEmailCcConfiguration(id: number) {
    const popup = this.dialog.open(UpdateEmailCcConfigurationComponent, {
      width: '800px', 
      height: '600px',
      enterAnimationDuration: '100ms',
      exitAnimationDuration: '100ms',
      data: {
        id: id
      }
    });
  }

  deleteEmailCcConfiguration(id: number) {
    this.dialogService.openConfirmDialog('Are you sure you want to delete this email CC configuration?')
      .afterClosed()
      .subscribe((res) => {
        if (res) {
          const deleteUri: string = `api/email-cc-configuration/${id}`;
          this.repoService.delete(deleteUri).subscribe((res) => {
            this.dialogService.openSuccessDialog("The email CC configuration has been deleted successfully.")
              .afterClosed()
              .subscribe((res) => {
                this.getEmailCcConfigurations();
              });
          });
        }
      });
  }

  toggleConfiguration(id: number) {
    const toggleUri: string = `api/email-cc-configuration/${id}/toggle`;
    this.repoService.create(toggleUri, {}).subscribe(
      (res) => {
        this.dialogService.openSuccessDialog("Configuration status has been updated successfully.")
          .afterClosed()
          .subscribe(() => {
            this.getEmailCcConfigurations();
          });
      },
      (error) => {
        this.dialogService.openErrorDialog("Error updating configuration status.");
      }
    );
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  formatEmails(emails: string[]): string {
    if (!emails || emails.length === 0) {
      return 'None';
    }
    return emails.join(', ');
  }
}