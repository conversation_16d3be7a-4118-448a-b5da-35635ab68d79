<!-- src/app/pages/ui-components/email-cc-configurations/update-email-cc-configuration/update-email-cc-configuration.component.html -->

<mat-card class="cardWithShadow theme-card modal-card">
  <mat-card-header>
    <mat-card-title class="m-b-0">Update Email CC Configuration</mat-card-title>
    <span class="flex-1-auto"></span>
    <button mat-flat-button matTooltipPosition="left" class="m-l-8" matTooltipHideDelay="100000"
      (click)="closeModal()">
      <mat-icon color="warn">close</mat-icon>
    </button>
  </mat-card-header>
  
  <mat-card-content class="b-t-1">
    <form [formGroup]="dataForm" autocomplete="off" novalidate (ngSubmit)="updateData(dataForm.value)">
      
      <!-- Display Config Key (Read-only) -->
      <div *ngIf="configuration">
        <mat-form-field class="w-100" appearance="outline">
          <mat-label>Config Key (Read-only)</mat-label>
          <input matInput [value]="configuration.configKey" readonly />
        </mat-form-field>
      </div>

      <!-- Config Name -->
      <mat-form-field class="w-100" appearance="outline">
        <mat-label>Config Name</mat-label>
        <input matInput formControlName="configName" placeholder="Display name for the configuration" />
        <mat-error *ngIf="validateControl('configName') && hasError('configName', 'required')">
          Config Name is required
        </mat-error>
        <mat-error *ngIf="validateControl('configName') && hasError('configName', 'maxlength')">
          Config Name cannot exceed 200 characters
        </mat-error>
      </mat-form-field>

      <!-- Description -->
      <mat-form-field class="w-100" appearance="outline">
        <mat-label>Description (Optional)</mat-label>
        <textarea matInput formControlName="description" rows="3" placeholder="Brief description of this configuration"></textarea>
        <mat-error *ngIf="validateControl('description') && hasError('description', 'maxlength')">
          Description cannot exceed 500 characters
        </mat-error>
      </mat-form-field>

      <!-- Status and Priority -->
      <div class="row">
        <div class="col-md-6">
          <mat-slide-toggle formControlName="isEnabled" class="m-t-16">
            Enable Configuration
          </mat-slide-toggle>
        </div>
        
        <div class="col-md-6">
          <mat-form-field class="w-100" appearance="outline">
            <mat-label>Priority</mat-label>
            <input matInput type="number" formControlName="priority" min="1" />
            <mat-error *ngIf="validateControl('priority') && hasError('priority', 'required')">
              Priority is required
            </mat-error>
            <mat-error *ngIf="validateControl('priority') && hasError('priority', 'min')">
              Priority must be at least 1
            </mat-error>
          </mat-form-field>
        </div>
      </div>

      <!-- Default CC Emails -->
      <div class="m-t-16">
        <h6>Default CC Emails</h6>
        <div formArrayName="defaultCcEmails">
          <div *ngFor="let email of defaultCcEmails.controls; let i = index" class="row align-items-center m-b-8">
            <div class="col-10">
              <mat-form-field class="w-100" appearance="outline">
                <mat-label>CC Email {{i + 1}}</mat-label>
                <input matInput [formControlName]="i" type="email" placeholder="<EMAIL>" />
                <mat-error *ngIf="validateEmailControl(defaultCcEmails, i) && hasEmailError(defaultCcEmails, i, 'email')">
                  Please enter a valid email address
                </mat-error>
              </mat-form-field>
            </div>
            <div class="col-2">
              <button mat-icon-button type="button" color="warn" (click)="removeCcEmail(i)" 
                      [disabled]="defaultCcEmails.length <= 1">
                <mat-icon>remove_circle</mat-icon>
              </button>
            </div>
          </div>
        </div>
        <button mat-stroked-button type="button" color="primary" (click)="addCcEmail()" class="m-t-8">
          <mat-icon>add</mat-icon> Add CC Email
        </button>
      </div>

      <!-- Default BCC Emails -->
      <div class="m-t-16">
        <h6>Default BCC Emails</h6>
        <div formArrayName="defaultBccEmails">
          <div *ngFor="let email of defaultBccEmails.controls; let i = index" class="row align-items-center m-b-8">
            <div class="col-10">
              <mat-form-field class="w-100" appearance="outline">
                <mat-label>BCC Email {{i + 1}}</mat-label>
                <input matInput [formControlName]="i" type="email" placeholder="<EMAIL>" />
                <mat-error *ngIf="validateEmailControl(defaultBccEmails, i) && hasEmailError(defaultBccEmails, i, 'email')">
                  Please enter a valid email address
                </mat-error>
              </mat-form-field>
            </div>
            <div class="col-2">
              <button mat-icon-button type="button" color="warn" (click)="removeBccEmail(i)" 
                      [disabled]="defaultBccEmails.length <= 1">
                <mat-icon>remove_circle</mat-icon>
              </button>
            </div>
          </div>
        </div>
        <button mat-stroked-button type="button" color="primary" (click)="addBccEmail()" class="m-t-8">
          <mat-icon>add</mat-icon> Add BCC Email
        </button>
      </div>

      <!-- Auto Add Settings -->
      <!-- <div class="m-t-24">
        <h6>Auto Add CC Settings</h6>
        <div class="row">
          <div class="col-md-4">
            <mat-slide-toggle formControlName="autoAddCcForOrders" class="m-t-8">
              Auto Add for Orders
            </mat-slide-toggle>
          </div>
          <div class="col-md-4">
            <mat-slide-toggle formControlName="autoAddCcForNotifications" class="m-t-8">
              Auto Add for Notifications
            </mat-slide-toggle>
          </div>
          <div class="col-md-4">
            <mat-slide-toggle formControlName="autoAddCcForAlerts" class="m-t-8">
              Auto Add for Alerts
            </mat-slide-toggle>
          </div>
        </div>
      </div> -->

      <!-- Form Actions -->
      <div class="m-t-24 text-right">
        <button mat-stroked-button type="button" class="m-r-8" (click)="closeModal()">
          Cancel
        </button>
        <button mat-flat-button color="primary" type="submit" [disabled]="!dataForm.valid">
          <mat-icon>save</mat-icon> Update Configuration
        </button>
      </div>
    </form>
  </mat-card-content>
</mat-card>