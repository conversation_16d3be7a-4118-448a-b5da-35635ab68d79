// src/app/pages/ui-components/email-cc-configurations/update-email-cc-configuration/update-email-cc-configuration.component.ts

import { Component, Inject, OnInit } from '@angular/core';
import { FormGroup, FormControl, Validators, FormArray } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { EmailCcConfigurationDto, EmailCcConfigurationForUpdateDto } from 'src/app/_interface/email-cc-configuration';
import { DataService } from 'src/app/shared/services/data.service';
import { DialogService } from 'src/app/shared/services/dialog.service';
import { RepositoryService } from 'src/app/shared/services/repository.service';

@Component({
  selector: 'app-update-email-cc-configuration',
  templateUrl: './update-email-cc-configuration.component.html',
})
export class UpdateEmailCcConfigurationComponent implements OnInit {
  dataForm!: FormGroup;
  configuration: EmailCcConfigurationDto | any;
  result: any;

  constructor( 
    private repoService: RepositoryService,
    private toastr: ToastrService,
    private dataService: DataService,
    private dialogService: DialogService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private dialogRef: MatDialogRef<UpdateEmailCcConfigurationComponent>
  ) {}

  ngOnInit() {
    this.dataForm = new FormGroup({
      configName: new FormControl('', [Validators.required, Validators.maxLength(200)]),
      isEnabled: new FormControl(true),
      description: new FormControl('', [Validators.maxLength(500)]),
      defaultCcEmails: new FormArray([]),
      defaultBccEmails: new FormArray([]),
      autoAddCcForOrders: new FormControl(false),
      autoAddCcForNotifications: new FormControl(false),
      autoAddCcForAlerts: new FormControl(false),
      priority: new FormControl(1, [Validators.required, Validators.min(1)])
    });

    this.result = this.data;
    this.getConfigurationToUpdate();
  }

  get defaultCcEmails(): FormArray {
    return this.dataForm.get('defaultCcEmails') as FormArray;
  }

  get defaultBccEmails(): FormArray {
    return this.dataForm.get('defaultBccEmails') as FormArray;
  }

  private setupEmailArrays(ccEmails: string[], bccEmails: string[]) {
    // Clear existing arrays
    while (this.defaultCcEmails.length !== 0) {
      this.defaultCcEmails.removeAt(0);
    }
    while (this.defaultBccEmails.length !== 0) {
      this.defaultBccEmails.removeAt(0);
    }

    // Add CC emails
    if (ccEmails && ccEmails.length > 0) {
      ccEmails.forEach(email => {
        this.defaultCcEmails.push(new FormControl(email, [Validators.email]));
      });
    } else {
      this.addCcEmail();
    }

    // Add BCC emails
    if (bccEmails && bccEmails.length > 0) {
      bccEmails.forEach(email => {
        this.defaultBccEmails.push(new FormControl(email, [Validators.email]));
      });
    } else {
      this.addBccEmail();
    }
  }

  addCcEmail() {
    const emailControl = new FormControl('', [Validators.email]);
    this.defaultCcEmails.push(emailControl);
  }

  removeCcEmail(index: number) {
    if (this.defaultCcEmails.length > 1) {
      this.defaultCcEmails.removeAt(index);
    }
  }

  addBccEmail() {
    const emailControl = new FormControl('', [Validators.email]);
    this.defaultBccEmails.push(emailControl);
  }

  removeBccEmail(index: number) {
    if (this.defaultBccEmails.length > 1) {
      this.defaultBccEmails.removeAt(index);
    }
  }

  public validateControl = (controlName: string) => {
    return this.dataForm?.get(controlName)?.invalid && this.dataForm?.get(controlName)?.touched
  }

  public hasError = (controlName: string, errorName: string) => {
    return this.dataForm?.get(controlName)?.hasError(errorName)
  }

  public validateEmailControl = (formArray: FormArray, index: number) => {
    const control = formArray.at(index);
    return control?.invalid && control?.touched;
  }

  public hasEmailError = (formArray: FormArray, index: number, errorName: string) => {
    const control = formArray.at(index);
    return control?.hasError(errorName);
  }

  public updateData = (dataFormValue: any) => {
    if (this.dataForm.valid) {
      this.executeDataUpdate(dataFormValue);
    }
  };

  private executeDataUpdate = (dataFormValue: any) => {
    // Filter out empty emails
    const ccEmails = dataFormValue.defaultCcEmails.filter((email: string) => email && email.trim() !== '');
    const bccEmails = dataFormValue.defaultBccEmails.filter((email: string) => email && email.trim() !== '');

    const data: EmailCcConfigurationForUpdateDto = {
      configName: dataFormValue.configName,
      isEnabled: dataFormValue.isEnabled,
      description: dataFormValue.description,
      defaultCcEmails: ccEmails,
      defaultBccEmails: bccEmails,
      autoAddCcForOrders: dataFormValue.autoAddCcForOrders,
      autoAddCcForNotifications: dataFormValue.autoAddCcForNotifications,
      autoAddCcForAlerts: dataFormValue.autoAddCcForAlerts,
      priority: dataFormValue.priority
    };

    const id = this.result.id;
    const uri: string = `api/email-cc-configuration/${id}`;
    this.repoService.update(uri, data).subscribe(
      (res) => {
        this.dialogService.openSuccessDialog("The email CC configuration has been updated successfully.")
          .afterClosed()
          .subscribe(() => {
            this.dataService.triggerRefreshTab1();
            this.closeModal();
          });
      },
      (error) => {
        this.toastr.error(error.error?.detail || "Error updating configuration");
      }
    );
  };

  closeModal() {
    this.dialogRef.close();
  }

  private getConfigurationToUpdate = () => {
    const id = this.result.id;
    const uri: string = `api/email-cc-configuration/${id}`;
    
    this.repoService.getData(uri)
      .subscribe({
        next: (config: EmailCcConfigurationDto | any) => {
          this.configuration = {...config};
          
          // Setup email arrays before patching the form
          this.setupEmailArrays(config.defaultCcEmails || [], config.defaultBccEmails || []);
          
          // Patch the form with configuration data (excluding email arrays as they're already set up)
          this.dataForm.patchValue({
            configName: config.configName,
            isEnabled: config.isEnabled,
            description: config.description,
            autoAddCcForOrders: config.autoAddCcForOrders,
            autoAddCcForNotifications: config.autoAddCcForNotifications,
            autoAddCcForAlerts: config.autoAddCcForAlerts,
            priority: config.priority
          });
        },
        error: (err) => {
          this.toastr.error("Error loading configuration");
          this.closeModal();
        }
      });
  }
}