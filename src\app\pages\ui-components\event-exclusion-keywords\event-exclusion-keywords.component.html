<mat-card class="cardWithShadow theme-card">
  <mat-card-header>
    <mat-card-title class="m-b-0">Event Exclusion Keywords</mat-card-title>
    <span class="flex-1-auto"></span>
    <button
      mat-flat-button
      color="primary"
      matTooltipPosition="left"
      class="m-l-8"
      matTooltipHideDelay="100000"
      (click)="addEventExclusionKeyword()"
    >
      <mat-icon>add</mat-icon>Add Exclusion Keyword
    </button>
  </mat-card-header>

  <mat-card-content class="b-t-1">
    <div class="table-responsive m-t-16">
      <table mat-table [dataSource]="dataSource" class="w-100">

        <ng-container matColumnDef="action">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="f-w-600 mat-subtitle-1 f-s-14"
          >
            Action
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            <button
              mat-flat-button
              color="primary"
              [matMenuTriggerFor]="actions"
              class="m-t-8"
            >
              Action <mat-icon>arrow_drop_down</mat-icon>
            </button>
            <mat-menu class="cardWithShadow" #actions="matMenu">
              <button mat-menu-item (click)="updateEventExclusionKeyword(element.id)">
                <mat-icon>edit</mat-icon>Edit
              </button>
              <button mat-menu-item (click)="deleteEventExclusionKeyword(element.id)">
                <mat-icon>delete</mat-icon>Delete
              </button>
            </mat-menu>
          </td>
        </ng-container>

        <ng-container matColumnDef="keyword">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="f-w-600 mat-subtitle-1 f-s-14"
          >
            Keyword
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            <span class="keyword-highlight">{{ element.keyword }}</span>
          </td>
        </ng-container>

        <ng-container matColumnDef="description">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="f-w-600 mat-subtitle-1 f-s-14"
          >
            Description
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            {{ element.description || 'No description' }}
          </td>
        </ng-container>

        <ng-container matColumnDef="isActive">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="f-w-600 mat-subtitle-1 f-s-14"
          >
            Status
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            <mat-chip 
              [color]="element.isActive ? 'primary' : 'warn'" 
              selected
            >
              {{ element.isActive ? 'Active' : 'Inactive' }}
            </mat-chip>
          </td>
        </ng-container>

        <ng-container matColumnDef="createdAt">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="f-w-600 mat-subtitle-1 f-s-14"
          >
            Created Date
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            {{ element.createdAt | date:'dd/MM/yyyy HH:mm' }}
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
    </div>
  </mat-card-content>
</mat-card>