import { HttpErrorResponse } from '@angular/common/http';
import { Component, OnInit, inject } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { DialogService } from 'src/app/shared/services/dialog.service';
import { RepositoryService } from 'src/app/shared/services/repository.service';
import { DataService } from 'src/app/shared/services/data.service';
import { EventExclusionKeywordDto } from 'src/app/_interface/event-exclusion.interface';
import { AddEventExclusionKeywordComponent } from './add-event-exclusion-keyword/add-event-exclusion-keyword.component';
import { UpdateCategoryComponent } from '../categories/update-category/update-category.component';
import { UpdateEventExclusionKeywordComponent } from './update-event-exclusion-keyword/update-event-exclusion-keyword.component';

@Component({
  selector: 'app-event-exclusion-keywords',
  templateUrl: './event-exclusion-keywords.component.html'
})
export class EventExclusionKeywordsComponent implements OnInit {
  
  displayedColumns: string[] = ['action', 'keyword', 'description', 'isActive', 'createdAt'];
  public dataSource = new MatTableDataSource<EventExclusionKeywordDto>();
  private refreshSubscription!: Subscription;

  constructor(
    private repoService: RepositoryService,
    private dialog: MatDialog,
    private toastr: ToastrService,
    private dataService: DataService,
    private dialogserve: DialogService
  ) {
    this.refreshSubscription = this.dataService.refreshTab1$.subscribe(() => {
      this.getEventExclusionKeywords();
    });
  }

  ngOnInit(): void {
    this.getEventExclusionKeywords();
  }

  ngOnDestroy(): void {
    if (this.refreshSubscription) {
      this.refreshSubscription.unsubscribe();
    }
  }

  public getEventExclusionKeywords() {
    this.repoService.getData('api/event-exclusion')
      .subscribe(res => {
        this.dataSource.data = res as EventExclusionKeywordDto[];
      },
      (err) => {
        console.log(err);
        this.toastr.error('Failed to load exclusion keywords');
      });
  }

  addEventExclusionKeyword() {
    const popup = this.dialog.open(AddEventExclusionKeywordComponent, {
      width: '600px', 
      height: '400px',
      enterAnimationDuration: '100ms',
      exitAnimationDuration: '100ms',
    });
  }

  updateEventExclusionKeyword(id: number) {
    const popup = this.dialog.open(UpdateEventExclusionKeywordComponent, {
      width: '600px', 
      height: '400px',
      enterAnimationDuration: '100ms',
      exitAnimationDuration: '100ms',
      data: {
        id: id
      }
    });
  }

  deleteEventExclusionKeyword(id: number) {
    this.dialogserve.openConfirmDialog('Are you sure you want to delete this exclusion keyword?')
      .afterClosed()
      .subscribe((res) => {
        if (res) {
          const deleteUri: string = `api/event-exclusion/${id}`;
          this.repoService.delete(deleteUri).subscribe((res) => {
            this.dialogserve.openSuccessDialog("The exclusion keyword has been deleted successfully.")
              .afterClosed()
              .subscribe((res) => {
                this.getEventExclusionKeywords();
              });
          },
          (error) => {
            this.toastr.error('Failed to delete exclusion keyword');
          });
        }
      });
  }

  toggleActive(id: number, currentStatus: boolean) {
    const newStatus = !currentStatus;
    const updateData = { isActive: newStatus };
    
    this.repoService.update(`api/event-exclusion/${id}`, updateData)
      .subscribe((res) => {
        this.toastr.success(`Exclusion keyword ${newStatus ? 'activated' : 'deactivated'} successfully`);
        this.getEventExclusionKeywords();
      },
      (error) => {
        this.toastr.error('Failed to update exclusion keyword status');
      });
  }

  testExclusion() {
    // Optional: Add a test dialog to check if an event title would be excluded
    const eventTitle = prompt('Enter event title to test:');
    if (eventTitle) {
      this.repoService.create('api/event-exclusion/check', eventTitle)
        .subscribe((res: any) => {
          const message = res.shouldExclude 
            ? `"${eventTitle}" WOULD BE EXCLUDED from KPI calculation`
            : `"${eventTitle}" would be included in KPI calculation`;
          this.toastr.info(message);
        },
        (error) => {
          this.toastr.error('Failed to test exclusion');
        });
    }
  }
}