<mat-card class="cardWithShadow theme-card modal-card">
  <mat-card-header>
    <mat-card-title class="m-b-0">Update Exclusion Keyword</mat-card-title>
    <span class="flex-1-auto"></span>
    <button mat-flat-button matTooltipPosition="left" class="m-l-8" matTooltipHideDelay="100000"
      (click)="closeModal()">
      <mat-icon color="warn">close</mat-icon>
    </button>
  </mat-card-header>
  <mat-card-content class="b-t-1">
    <form [formGroup]="dataForm" autocomplete="off" novalidate (ngSubmit)="updateData(dataForm.value)">
      
      <mat-form-field class="w-100" appearance="outline">
        <mat-label>Keyword *</mat-label>
        <input matInput formControlName="keyword" placeholder="e.g., đi công trình" />
        <mat-error *ngIf="hasError('keyword', 'required')">
          Keyword is required
        </mat-error>
        <mat-error *ngIf="hasError('keyword', 'maxlength')">
          Keyword cannot exceed 100 characters
        </mat-error>
      </mat-form-field>

      <mat-form-field class="w-100" appearance="outline">
        <mat-label>Description</mat-label>
        <textarea matInput formControlName="description" rows="3" 
          placeholder="Description of what this keyword excludes"></textarea>
        <mat-error *ngIf="hasError('description', 'maxlength')">
          Description cannot exceed 200 characters
        </mat-error>
      </mat-form-field>

      <div class="form-field-wrapper">
        <mat-checkbox formControlName="isActive">
          Active (will exclude events containing this keyword)
        </mat-checkbox>
      </div>

      <button mat-flat-button color="primary" [disabled]="!dataForm.valid">
        Save
      </button>
    </form>
  </mat-card-content>
</mat-card>