import { Component, OnInit, Inject } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { EventExclusionKeywordDto, EventExclusionKeywordForUpdateDto } from 'src/app/_interface/event-exclusion.interface';
import { DataService } from 'src/app/shared/services/data.service';
import { DialogService } from 'src/app/shared/services/dialog.service';
import { ErrorHandlerService } from 'src/app/shared/services/error-handler.service';
import { RepositoryService } from 'src/app/shared/services/repository.service';

@Component({
  selector: 'app-update-event-exclusion-keyword',
  templateUrl: './update-event-exclusion-keyword.component.html',
})
export class UpdateEventExclusionKeywordComponent implements OnInit {
  dataForm: FormGroup | any;
  exclusionKeyword!: EventExclusionKeywordDto;

  constructor(
    private repoService: RepositoryService,
    private toastr: ToastrService,
    private dataService: DataService,
    private dialogserve: DialogService,
    private Ref: MatDialogRef<UpdateEventExclusionKeywordComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.dataForm = new FormGroup({
      keyword: new FormControl('', [Validators.required, Validators.maxLength(100)]),
      description: new FormControl('', [Validators.maxLength(200)]),
      isActive: new FormControl(true),
    });

    this.getExclusionKeyword();
  }

  getExclusionKeyword() {
    const getUri: string = `api/event-exclusion/${this.data.id}`;
    this.repoService.getData(getUri).subscribe(
      (res) => {
        this.exclusionKeyword = res as EventExclusionKeywordDto;
        this.dataForm.patchValue({
          keyword: this.exclusionKeyword.keyword,
          description: this.exclusionKeyword.description,
          isActive: this.exclusionKeyword.isActive,
        });
      },
      (error) => {
        this.toastr.error('Failed to load exclusion keyword');
        this.Ref.close([]);
      }
    );
  }

  public validateControl = (controlName: string) => {
    return this.dataForm?.get(controlName)?.invalid && this.dataForm?.get(controlName)?.touched;
  };

  public hasError = (controlName: string, errorName: string) => {
    return this.dataForm?.get(controlName)?.hasError(errorName);
  };

  public updateData = (dataFormValue: any) => {
    if (this.dataForm.valid) {
      this.executeDataUpdate(dataFormValue);
    }
  };

  private executeDataUpdate = (dataFormValue: any) => {
    let data: EventExclusionKeywordForUpdateDto = {
      keyword: dataFormValue.keyword,
      description: dataFormValue.description,
      isActive: dataFormValue.isActive,
    };

    const updateUri: string = `api/event-exclusion/${this.data.id}`;
    this.repoService.update(updateUri, data).subscribe(
      (res) => {
        this.dialogserve.openSuccessDialog("The exclusion keyword has been updated successfully.")
          .afterClosed()
          .subscribe((res) => {
            this.dataService.triggerRefreshTab1();
            this.Ref.close([]);
          });
      },
      (error) => {
        this.toastr.error(error);
        this.Ref.close([]);
      }
    );
  };

  closeModal() {
    this.Ref.close([]);
  }
}