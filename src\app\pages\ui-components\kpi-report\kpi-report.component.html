<mat-card class="cardWithShadow theme-card">
  <mat-card-header>
    <mat-card-title class="m-b-0">KPI Report</mat-card-title>
    <span class="flex-1-auto"></span>
  </mat-card-header>

  <mat-card-content class="b-t-1">
    <!-- Bộ lọc -->
    <div class="filters m-t-16 m-b-16">
      <div class="row g-3">
        <!-- Start Date -->
        <div class="col-12 col-md-6 col-lg-6">
          <mat-form-field appearance="outline" class="filter-field w-100">
            <mat-label>Start Date</mat-label>
            <input matInput [matDatepicker]="startPicker" [(ngModel)]="startDate">
            <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
            <mat-datepicker #startPicker></mat-datepicker>
          </mat-form-field>
        </div>
        <!-- End Date -->
        <div class="col-12 col-md-6 col-lg-6">
          <mat-form-field appearance="outline" class="filter-field w-100">
            <mat-label>End Date</mat-label>
            <input matInput [matDatepicker]="endPicker" [(ngModel)]="endDate">
            <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
            <mat-datepicker #endPicker></mat-datepicker>
          </mat-form-field>
        </div>
      </div>
      <!-- Search | Reset | Export -->
      <div class="row g-3 mt-2">
        <div class="col-12 col-md-6">
          <button mat-flat-button color="primary" class="filter-button w-100" (click)="onSubmit()">
            <mat-icon>search</mat-icon> Generate Report
          </button>
        </div>
        <div class="col-12 col-md-6">
          <button mat-flat-button color="accent" class="filter-button w-100" (click)="exportKpiReport()">
            <mat-icon>file_download</mat-icon> Export
          </button>
        </div>
      </div>
    </div>

    <div class="table-responsive m-t-16">
      <mat-form-field class="w-100" appearance="outline">
        <input matInput type="text" (keyup)="doFilter($any($event).target.value)" placeholder="Search...">
      </mat-form-field>
      <table mat-table [dataSource]="dataSource" class="w-100" matSort>
        <ng-container matColumnDef="userName">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14" mat-sort-header>User</th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">{{ element.userName }}</td>
        </ng-container>

        <ng-container matColumnDef="smallOrders">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14" mat-sort-header>Small</th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">{{ element.smallOrders }}</td>
        </ng-container>

        <ng-container matColumnDef="mediumOrders">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14" mat-sort-header>Medium</th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">{{ element.mediumOrders }}</td>
        </ng-container>

        <ng-container matColumnDef="largeOrders">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14" mat-sort-header>Large</th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">{{ element.largeOrders }}</td>
        </ng-container>

        <ng-container matColumnDef="averageStars">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14" mat-sort-header>Rating</th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            {{ element.averageStars }}
          </td>
        </ng-container>

        <ng-container matColumnDef="rewardOrPenalty">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14" mat-sort-header>Reward</th>
          <td mat-cell *matCellDef="let element" class="mat-body-1" 
              [ngClass]="{'text-success': element.rewardOrPenalty > 0, 'text-danger': element.rewardOrPenalty < 0}">
            {{ formatCurrency(element.rewardOrPenalty) }}
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
    </div>

    <mat-paginator [pageSize]="10" [pageSizeOptions]="[5, 10, 15, 20]" showFirstLastButtons></mat-paginator>
  </mat-card-content>
</mat-card>

