import { AfterViewInit, Component, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { RepositoryService } from 'src/app/shared/services/repository.service';
import { ToastrService } from 'ngx-toastr';
import { KpiReportDto } from 'src/app/_interface/kpi-report';
import { DatePipe } from '@angular/common';
import { Subscription } from 'rxjs';
import { DialogService } from 'src/app/shared/services/dialog.service';

@Component({
  selector: 'app-kpi-report',
  templateUrl: './kpi-report.component.html',
  styleUrls: ['./kpi-report.component.scss'],
  providers: [DatePipe]
})
export class KpiReportComponent implements OnInit, AfterViewInit, OnDestroy {
  displayedColumns: string[] = ['userName', 'smallOrders', 'mediumOrders', 'largeOrders', 'averageStars', 'rewardOrPenalty'];
  public dataSource = new MatTableDataSource<KpiReportDto>();
  
  // Direct date properties instead of FormGroup
  startDate: Date = new Date(); // Default to today
  endDate: Date = new Date(); // Default to today
  
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;
  
  private subscriptions = new Subscription();

  constructor(
    private repoService: RepositoryService,
    private toastr: ToastrService,
    private datePipe: DatePipe,
    private dialogServe: DialogService
  ) { }
  
  ngOnInit(): void {
    // Initialize with date range from 21st of previous month to 20th of current month
    const today = new Date();
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();
    
    // For start date: 21st of previous month
    this.startDate = new Date(currentYear, currentMonth - 1, 21);
    
    // For end date: 20th of current month
    this.endDate = new Date(currentYear, currentMonth, 20);
    
    this.getKpiReport();
  }

  ngAfterViewInit(): void {
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  public getKpiReport() {
    if (!this.startDate || !this.endDate) {
      this.dialogServe.openErrorDialog('Please provide valid date range');
      return;
    }
    
    const startDate = this.formatDate(this.startDate);
    const endDate = this.formatDate(this.endDate);
    
    if (this.startDate > this.endDate) {
      this.dialogServe.openErrorDialog('Start date must be less than or equal to end date.');
      return;
    }
    
    const apiUrl = `api/calendar-report/kpi-summary?startDate=${startDate}&endDate=${endDate}`;
    
    this.repoService.getData(apiUrl)
      .subscribe({
        next: (res) => {
          this.dataSource.data = res as KpiReportDto[];
          if (this.dataSource.data.length === 0) {
            this.toastr.info('No data found for the selected date range');
          }
        },
        error: (err) => {
          this.handleError('KPI report', err);
        }
      });
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  };

  private formatDate(date: Date): string {
    return this.datePipe.transform(date, 'yyyy/MM/dd') || '';
  }

  public formatCurrency(amount: number): string {
    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(amount);
  }

  public getStarRating(stars: number): string {
    const fullStars = Math.floor(stars);
    const halfStar = stars % 1 >= 0.5;
    let starString = '';
    
    for (let i = 0; i < fullStars; i++) {
      starString += '★';
    }
    
    if (halfStar) {
      starString += '½';
    }
    
    return starString;
  }

  public onSubmit() {
    this.getKpiReport();
  }

  public exportKpiReport() {
    if (!this.startDate || !this.endDate) {
      this.dialogServe.openErrorDialog('Please provide valid date range');
      return;
    }
    
    const startDate = this.formatDate(this.startDate);
    const endDate = this.formatDate(this.endDate);
    
    if (this.startDate > this.endDate) {
      this.dialogServe.openErrorDialog('Start date must be less than or equal to end date.');
      return;
    }
    
    const params = {
      startDate: startDate,
      endDate: endDate
    };
    
    this.repoService.exportReport('api/calendar-report/export', params)
      .subscribe({
        next: (blob: Blob) => {
          if (blob && blob.size > 0) {
            this.downloadFile(blob, `KPI_Report_${this.formatDateForFileName(this.startDate)}_${this.formatDateForFileName(this.endDate)}.xlsx`);
          } else {
            this.dialogServe.openErrorDialog('No data to export');
          }
        },
        error: (err) => {
          this.handleError('exporting KPI report', err);
        }
      });
  }
  
  private formatDateForFileName(date: Date): string {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}${month}${year}`;
  }
  
  private downloadFile(blob: Blob, fileName: string): void {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }
  
  private handleError(type: string, err: any): void {
    console.error(`Error with ${type}:`, err);
    this.toastr.error(`Error with ${type}: ${err.message || 'Unknown error'}`);
  }
}





