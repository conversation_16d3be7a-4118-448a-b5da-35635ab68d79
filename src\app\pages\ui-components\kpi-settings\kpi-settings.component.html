<mat-card class="cardWithShadow theme-card">
  <mat-card-header>
    <mat-card-title class="m-b-0"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></mat-card-title>
    <span class="flex-1-auto"></span>
  </mat-card-header>

  <mat-card-content class="b-t-1">

    <form [formGroup]="kpiForm" (ngSubmit)="onSubmit()" *ngIf="!isLoading">
      <!-- C<PERSON><PERSON> h<PERSON><PERSON> Chấm điểm Sao -->
      <mat-expansion-panel class="m-b-16">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <mat-icon class="m-r-8">star</mat-icon>
            Sao
          </mat-panel-title>
        </mat-expansion-panel-header>
        
        <div class="row g-3">
          <div class="col-12 col-sm-6 col-md-6 col-lg-4">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label><PERSON><PERSON><PERSON> thành sớm</mat-label>
              <input matInput type="number" formControlName="stars_EarlyCompletion" min="1" max="5">
              <mat-error>{{ getFieldError('stars_EarlyCompletion') }}</mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-sm-6 col-md-6 col-lg-4">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Đúng hạn</mat-label>
              <input matInput type="number" formControlName="stars_OnTime" min="1" max="5">
              <mat-error>{{ getFieldError('stars_OnTime') }}</mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-sm-6 col-md-6 col-lg-4">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Trễ 1 ngày</mat-label>
              <input matInput type="number" formControlName="stars_Late1Day" min="1" max="5">
              <mat-error>{{ getFieldError('stars_Late1Day') }}</mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-sm-6 col-md-6 col-lg-4">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Trễ 2 ngày</mat-label>
              <input matInput type="number" formControlName="stars_Late2Days" min="1" max="5">
              <mat-error>{{ getFieldError('stars_Late2Days') }}</mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-sm-6 col-md-6 col-lg-4">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Trễ 3+ ngày</mat-label>
              <input matInput type="number" formControlName="stars_Late3OrMoreDays" min="1" max="5">
              <mat-error>{{ getFieldError('stars_Late3OrMoreDays') }}</mat-error>
            </mat-form-field>
          </div>
        </div>
        
        <div class="alert alert-info m-t-16">
          <strong> {{ calculateStarsExample() }} </strong> 
        </div>
      </mat-expansion-panel>

      <!-- Phân loại Đơn hàng -->
      <mat-expansion-panel class="m-b-16">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <mat-icon class="m-r-8">category</mat-icon>Đơn hàng
          </mat-panel-title>
        </mat-expansion-panel-header>
        
        <div class="row g-3">
          <div class="col-12 col-sm-6 col-md-6 col-lg-3">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Đơn nhỏ (tối đa ngày)</mat-label>
              <input matInput type="number" formControlName="lightOrder_MaxDays" min="1">
              <mat-error>{{ getFieldError('lightOrder_MaxDays') }}</mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-sm-6 col-md-6 col-lg-3">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Đơn vừa (tối thiểu)</mat-label>
              <input matInput type="number" formControlName="mediumOrder_MinDays" min="1">
              <mat-error>{{ getFieldError('mediumOrder_MinDays') }}</mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-sm-6 col-md-6 col-lg-3">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Đơn vừa (tối đa)</mat-label>
              <input matInput type="number" formControlName="mediumOrder_MaxDays" min="1">
              <mat-error>{{ getFieldError('mediumOrder_MaxDays') }}</mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-sm-6 col-md-6 col-lg-3">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Đơn lớn (tối thiểu)</mat-label>
              <input matInput type="number" formControlName="heavyOrder_MinDays" min="1">
              <mat-error>{{ getFieldError('heavyOrder_MinDays') }}</mat-error>
            </mat-form-field>
          </div>
        </div>
        
        <div class="alert alert-info m-t-16">
          <strong>Công thức:</strong><br>
          • Đơn nhỏ (SLn): &lt; {{ kpiForm.get('lightOrder_MaxDays')?.value || 5 }} ngày<br>
          • Đơn vừa (SLv): {{ kpiForm.get('mediumOrder_MinDays')?.value || 5 }} - {{ kpiForm.get('mediumOrder_MaxDays')?.value || 19 }} ngày<br>
          • đơn lớn (SLl): ≥ {{ kpiForm.get('heavyOrder_MinDays')?.value || 20 }} ngày
        </div>
      </mat-expansion-panel>

      <!-- Hệ số Số lượng (HSSL) -->
      <mat-expansion-panel class="m-b-16">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <mat-icon class="m-r-8">calculate</mat-icon>
            HSSL
          </mat-panel-title>
        </mat-expansion-panel-header>
        
        <div class="row g-3">
          <div class="col-12 col-sm-6 col-md-6 col-lg-3">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Số đơn nhỏ đầu tiên không tính điểm</mat-label>
              <input matInput type="number" formControlName="hssL_LightOrderFreeCount" min="0">
              <mat-error>{{ getFieldError('hssL_LightOrderFreeCount') }}</mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-sm-6 col-md-6 col-lg-3">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Hệ số đơn nhỏ</mat-label>
              <input matInput type="number" step="0.1" formControlName="hssL_LightOrderMultiplier" min="0">
              <mat-error>{{ getFieldError('hssL_LightOrderMultiplier') }}</mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-sm-6 col-md-6 col-lg-3">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Hệ số đơn vừa</mat-label>
              <input matInput type="number" step="0.1" formControlName="hssL_MediumOrderMultiplier" min="0">
              <mat-error>{{ getFieldError('hssL_MediumOrderMultiplier') }}</mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-sm-6 col-md-6 col-lg-3">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Hệ số đơn lớn</mat-label>
              <input matInput type="number" step="0.1" formControlName="hssL_HeavyOrderMultiplier" min="0">
              <mat-error>{{ getFieldError('hssL_HeavyOrderMultiplier') }}</mat-error>
            </mat-form-field>
          </div>
        </div>
        
        <div class="alert alert-info m-t-16">
          <strong>Công thức HSSL:</strong><br>
          HSSL = [MAX(SLn - {{ kpiForm.get('hssL_LightOrderFreeCount')?.value || 5 }}, 0)] × {{ kpiForm.get('hssL_LightOrderMultiplier')?.value || 0.1 }} + SLv × {{ kpiForm.get('hssL_MediumOrderMultiplier')?.value || 1 }} + SLl × {{ kpiForm.get('hssL_HeavyOrderMultiplier')?.value || 2 }}<br>
        </div>
      </mat-expansion-panel>
      <!-- Cấu hình Thưởng/Phạt -->
      <mat-expansion-panel class="m-b-16">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <mat-icon class="m-r-8">monetization_on</mat-icon>
            Thưởng/Phạt
          </mat-panel-title>
        </mat-expansion-panel-header>
        
        <div class="row g-3">
          <!-- High Performance -->
          <div class="col-12">
            <h4>Hiệu suất cao (Thưởng)</h4>
          </div>
          <div class="col-12 col-md-6 col-lg-3">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Sao tối thiểu</mat-label>
              <input matInput type="number" step="0.1" formControlName="reward_HighPerformance_MinStars" min="0" max="5">
              <mat-error>{{ getFieldError('reward_HighPerformance_MinStars') }}</mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-md-6 col-lg-3">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Sao tối đa</mat-label>
              <input matInput type="number" step="0.1" formControlName="reward_HighPerformance_MaxStars" min="0" max="5">
              <mat-error>{{ getFieldError('reward_HighPerformance_MaxStars') }}</mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-md-6 col-lg-3">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Số tiền cơ sở</mat-label>
              <input matInput type="number" formControlName="reward_BaseAmount" min="0">
              <mat-error>{{ getFieldError('reward_BaseAmount') }}</mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-md-6 col-lg-3">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Phạt cơ sở</mat-label>
              <input matInput type="number" formControlName="reward_BasePenalty" min="0">
              <mat-error>{{ getFieldError('reward_BasePenalty') }}</mat-error>
            </mat-form-field>
          </div>

          <!-- Medium Performance -->
          <div class="col-12">
            <h4>Hiệu suất trung bình (Phạt nhỏ)</h4>
          </div>
          <div class="col-12 col-md-6">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Sao tối thiểu</mat-label>
              <input matInput type="number" step="0.1" formControlName="penalty_MediumPerformance_MinStars" min="0" max="5">
              <mat-error>{{ getFieldError('penalty_MediumPerformance_MinStars') }}</mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-md-6">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Sao tối đa</mat-label>
              <input matInput type="number" step="0.1" formControlName="penalty_MediumPerformance_MaxStars" min="0" max="5">
              <mat-error>{{ getFieldError('penalty_MediumPerformance_MaxStars') }}</mat-error>
            </mat-form-field>
          </div>

          <!-- Low Performance -->
          <div class="col-12">
            <h4>Hiệu suất thấp (Phạt nặng)</h4>
          </div>
          <div class="col-12 col-md-6 col-lg-3">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Sao tối thiểu</mat-label>
              <input matInput type="number" step="0.1" formControlName="penalty_LowPerformance_MinStars" min="0" max="5">
              <mat-error>{{ getFieldError('penalty_LowPerformance_MinStars') }}</mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-md-6 col-lg-3">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Sao tối đa</mat-label>
              <input matInput type="number" step="0.1" formControlName="penalty_LowPerformance_MaxStars" min="0" max="5">
              <mat-error>{{ getFieldError('penalty_LowPerformance_MaxStars') }}</mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-md-6 col-lg-3">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Số tiền cơ sở</mat-label>
              <input matInput type="number" formControlName="penalty_LowPerformance_BaseAmount" min="0">
              <mat-error>{{ getFieldError('penalty_LowPerformance_BaseAmount') }}</mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-md-6 col-lg-3">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Phạt tối đa</mat-label>
              <input matInput type="number" formControlName="penalty_LowPerformance_MaxPenalty" min="0">
              <mat-error>{{ getFieldError('penalty_LowPerformance_MaxPenalty') }}</mat-error>
            </mat-form-field>
          </div>
        </div>
        
        <div class="alert alert-info m-t-16">
          <strong>Công thức Thưởng/Phạt:</strong><br>
          • <strong>Hiệu suất cao ({{ kpiForm.get('reward_HighPerformance_MinStars')?.value || 3.4 }} ≤ TD ≤ {{ kpiForm.get('reward_HighPerformance_MaxStars')?.value || 5 }}):</strong><br>
          &nbsp;&nbsp;Thưởng = ((TD - 3) × {{ formatCurrency(kpiForm.get('reward_BaseAmount')?.value || 2500000) }} / 2 - {{ formatCurrency(kpiForm.get('reward_BasePenalty')?.value || 500000) }} - PB) × HSSL<br>
          • <strong>Hiệu suất trung bình ({{ kpiForm.get('penalty_MediumPerformance_MinStars')?.value || 3 }} ≤ TD &lt; {{ kpiForm.get('penalty_MediumPerformance_MaxStars')?.value || 3.4 }}):</strong><br>
          &nbsp;&nbsp;Phạt nhỏ = (TD - 3) × {{ formatCurrency(kpiForm.get('reward_BaseAmount')?.value || 2500000) }} / 2 - {{ formatCurrency(kpiForm.get('reward_BasePenalty')?.value || 500000) }} + PB<br>
          • <strong>Hiệu suất thấp ({{ kpiForm.get('penalty_LowPerformance_MinStars')?.value || 1 }} ≤ TD &lt; {{ kpiForm.get('penalty_LowPerformance_MaxStars')?.value || 3 }}):</strong><br>
          &nbsp;&nbsp;Phạt nặng = (TD - 1) × {{ formatCurrency(kpiForm.get('penalty_LowPerformance_BaseAmount')?.value || 500000) }} / 2 - {{ formatCurrency(kpiForm.get('penalty_LowPerformance_MaxPenalty')?.value || 1000000) }} + PB
        </div>
      </mat-expansion-panel>

      <!-- Mô tả -->
      <mat-expansion-panel class="m-b-16">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <mat-icon class="m-r-8">description</mat-icon>
            Mô tả
          </mat-panel-title>
        </mat-expansion-panel-header>
        
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Mô tả cấu hình</mat-label>
          <textarea matInput formControlName="description" rows="3" placeholder="Nhập mô tả cho cấu hình này..."></textarea>
          <mat-error>{{ getFieldError('description') }}</mat-error>
        </mat-form-field>
      </mat-expansion-panel>

      <!-- Buttons -->
      <div class="row g-3 m-t-16">
        <div class="col-12 col-md-12">
          <button mat-flat-button color="primary" type="submit" class="w-100" [disabled]="isLoading || kpiForm.invalid">
            <mat-icon>save</mat-icon> Lưu cấu hình
          </button>
        </div>
        
      </div>
    </form>
  </mat-card-content>
</mat-card>