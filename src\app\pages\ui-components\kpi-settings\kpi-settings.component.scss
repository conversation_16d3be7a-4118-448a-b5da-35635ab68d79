.kpi-settings {
  .mat-expansion-panel {
    margin-bottom: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .mat-expansion-panel-header {
      height: 64px;
      
      .mat-panel-title {
        display: flex;
        align-items: center;
        font-weight: 500;
        
        mat-icon {
          margin-right: 8px;
          color: #2196f3;
        }
      }
    }

    .mat-expansion-panel-content {
      .mat-expansion-panel-body {
        padding: 16px 24px 24px;
      }
    }
  }

  .alert {
    padding: 12px 16px;
    border-radius: 4px;
    margin-top: 16px;
    
    &.alert-info {
      background-color: #e3f2fd;
      border: 1px solid #bbdefb;
      color: #0d47a1;
      
      strong {
        color: #1565c0;
      }
    }
  }

  .mat-form-field {
    .mat-form-field-hint {
      font-size: 11px;
      color: #666;
    }
  }

  .config-info {
    background-color: #f5f5f5;
    padding: 16px;
    border-radius: 8px;
    margin-top: 24px;
    
    .mat-chip {
      font-size: 12px;
      font-weight: 500;
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    
    .mat-spinner {
      margin-bottom: 16px;
    }
    
    p {
      color: #666;
      margin: 0;
    }
  }

  h4 {
    color: #424242;
    font-weight: 500;
    margin: 16px 0 8px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #e0e0e0;
    
    &:first-of-type {
      margin-top: 0;
    }
  }

  .button-group {
    .mat-flat-button {
      height: 48px;
      font-weight: 500;
      
      mat-icon {
        margin-right: 8px;
      }
    }
  }

  // Mobile responsive adjustments
  @media (max-width: 768px) {
    .mat-card {
      margin: 8px;
      border-radius: 12px;
    }

    .mat-card-header {
      padding: 16px;
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
      
      .mat-card-title {
        font-size: 18px;
        margin-bottom: 0;
      }
      
      button {
        width: 100%;
        height: 44px;
        font-size: 14px;
      }
    }

    .mat-expansion-panel {
      margin-bottom: 12px;
      
      .mat-expansion-panel-header {
        height: auto;
        min-height: 56px;
        padding: 16px;
        
        .mat-panel-title {
          font-size: 16px;
          font-weight: 500;
          
          mat-icon {
            margin-right: 8px;
            font-size: 20px;
          }
        }
        
        .mat-panel-description {
          font-size: 13px;
          margin-top: 4px;
          color: #666;
          line-height: 1.3;
        }
      }
    }
    
    .mat-expansion-panel-content .mat-expansion-panel-body {
      padding: 16px;
    }

    // Form fields on mobile
    .mat-form-field {
      margin-bottom: 16px;
      
      .mat-form-field-label {
        font-size: 14px;
      }
      
      .mat-form-field-hint {
        font-size: 12px;
        line-height: 1.4;
        margin-top: 4px;
      }
      
      input {
        font-size: 16px; // Prevent zoom on iOS
      }
    }
    
    .alert {
      padding: 12px;
      font-size: 13px;
      line-height: 1.4;
      border-radius: 8px;
      
      strong {
        display: block;
        margin-bottom: 6px;
        font-size: 14px;
      }
    }

    // Button improvements for mobile
    .mat-flat-button {
      height: 48px;
      font-size: 14px;
      font-weight: 500;
      border-radius: 8px;
      
      mat-icon {
        margin-right: 8px;
        font-size: 18px;
      }
    }

    // Section headers on mobile
    h4 {
      font-size: 16px;
      margin: 20px 0 12px 0;
      padding-bottom: 8px;
      color: #333;
    }

    // Loading state on mobile
    .loading-container {
      min-height: 150px;
      padding: 20px;
      
      .mat-spinner {
        width: 40px;
        height: 40px;
        margin-bottom: 12px;
      }
      
      p {
        font-size: 14px;
      }
    }

    // Config info section
    .config-info {
      padding: 12px;
      margin-top: 16px;
      
      p {
        font-size: 13px;
        margin-bottom: 8px;
      }
      
      .mat-chip {
        font-size: 11px;
        height: 24px;
      }
    }
  }

  // Extra small devices (phones in portrait)
  @media (max-width: 480px) {
    .mat-card {
      margin: 4px;
    }

    .mat-card-header {
      padding: 12px;
      
      .mat-card-title {
        font-size: 16px;
      }
    }

    .mat-expansion-panel-header {
      padding: 12px !important;
      
      .mat-panel-title {
        font-size: 14px;
        
        mat-icon {
          font-size: 18px;
          margin-right: 6px;
        }
      }
      
      .mat-panel-description {
        font-size: 12px;
      }
    }

    .mat-expansion-panel-content .mat-expansion-panel-body {
      padding: 12px !important;
    }

    .alert {
      padding: 10px;
      font-size: 12px;
      
      strong {
        font-size: 13px;
      }
    }

    h4 {
      font-size: 15px;
      margin: 16px 0 10px 0;
    }
  }
}

// Enhanced responsive grid system
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px;
  
  > * {
    padding: 0 8px;
    margin-bottom: 16px;
  }
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}

// Tablet breakpoint
@media (min-width: 576px) {
  .col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
}

// Desktop breakpoint
@media (min-width: 768px) {
  .row {
    margin: 0 -12px;
    
    > * {
      padding: 0 12px;
    }
  }

  .col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
}

// Large desktop breakpoint
@media (min-width: 992px) {
  .col-lg-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }

  .col-lg-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
}

// Mobile-first button styles
.button-group {
  .mat-flat-button {
    width: 100%;
    height: 48px;
    font-weight: 500;
    margin-bottom: 12px;
    border-radius: 8px;
    
    mat-icon {
      margin-right: 8px;
    }

    @media (min-width: 768px) {
      margin-bottom: 0;
    }
  }
}

// Mobile spacing utilities
.m-t-8 { margin-top: 8px; }
.m-t-12 { margin-top: 12px; }
.m-b-8 { margin-bottom: 8px; }
.m-b-12 { margin-bottom: 12px; }
.p-8 { padding: 8px; }
.p-12 { padding: 12px; }

// Touch-friendly sizing
@media (max-width: 768px) {
  .mat-form-field {
    .mat-form-field-infix {
      padding: 16px 0;
    }
  }
  
  .mat-button, .mat-raised-button, .mat-flat-button {
    min-height: 44px;
    padding: 0 16px;
  }
  
  .mat-expansion-panel-header {
    min-height: 56px;
  }
}