// import { Component, OnInit, OnDestroy } from '@angular/core';
// import { FormBuilder, FormGroup, Validators } from '@angular/forms';
// import { RepositoryService } from 'src/app/shared/services/repository.service';
// import { ToastrService } from 'ngx-toastr';
// import { DialogService } from 'src/app/shared/services/dialog.service';
// import { Subscription } from 'rxjs';

// export interface KpiConfiguration {
//   id: number;
//   stars_EarlyCompletion: number;
//   stars_OnTime: number;
//   stars_Late1Day: number;
//   stars_Late2Days: number;
//   stars_Late3OrMoreDays: number;
//   lightOrder_MaxDays: number;
//   mediumOrder_MinDays: number;
//   mediumOrder_MaxDays: number;
//   heavyOrder_MinDays: number;
//   hssL_LightOrderFreeCount: number;
//   hssL_LightOrderMultiplier: number;
//   hssL_MediumOrderMultiplier: number;
//   hssL_HeavyOrderMultiplier: number;
//   penalty_HeavyError: number;
//   penalty_LightError: number;
//   penalty_NoError: number;
//   reward_HighPerformance_MinStars: number;
//   reward_HighPerformance_MaxStars: number;
//   reward_BaseAmount: number;
//   reward_BasePenalty: number;
//   penalty_MediumPerformance_MinStars: number;
//   penalty_MediumPerformance_MaxStars: number;
//   penalty_LowPerformance_MinStars: number;
//   penalty_LowPerformance_MaxStars: number;
//   penalty_LowPerformance_BaseAmount: number;
//   penalty_LowPerformance_MaxPenalty: number;
//   createdAt: string;
//   updatedAt?: string;
//   description: string;
//   isActive: boolean;
// }

// @Component({
//   selector: 'app-kpi-settings',
//   templateUrl: './kpi-settings.component.html',
//   styleUrls: ['./kpi-settings.component.scss']
// })
// export class KpiSettingsComponent implements OnInit, OnDestroy {
//   kpiForm!: FormGroup;
//   currentConfig?: KpiConfiguration;
//   isLoading = false;
  
//   private subscriptions = new Subscription();

//   constructor(
//     private fb: FormBuilder,
//     private repoService: RepositoryService,
//     private toastr: ToastrService,
//     private dialogService: DialogService
//   ) { }

//   ngOnInit(): void {
//     this.initializeForm();
//     this.loadActiveConfiguration();
//   }

//   ngOnDestroy(): void {
//     this.subscriptions.unsubscribe();
//   }

//   private initializeForm(): void {
//     this.kpiForm = this.fb.group({
//       // Stars Configuration
//       stars_EarlyCompletion: [5, [Validators.required, Validators.min(1), Validators.max(5)]],
//       stars_OnTime: [4, [Validators.required, Validators.min(1), Validators.max(5)]],
//       stars_Late1Day: [3, [Validators.required, Validators.min(1), Validators.max(5)]],
//       stars_Late2Days: [2, [Validators.required, Validators.min(1), Validators.max(5)]],
//       stars_Late3OrMoreDays: [1, [Validators.required, Validators.min(1), Validators.max(5)]],

//       // Order Categorization
//       lightOrder_MaxDays: [5, [Validators.required, Validators.min(1)]],
//       mediumOrder_MinDays: [5, [Validators.required, Validators.min(1)]],
//       mediumOrder_MaxDays: [19, [Validators.required, Validators.min(1)]],
//       heavyOrder_MinDays: [20, [Validators.required, Validators.min(1)]],

//       // HSSL Configuration
//       hssL_LightOrderFreeCount: [5, [Validators.required, Validators.min(0)]],
//       hssL_LightOrderMultiplier: [0.1, [Validators.required, Validators.min(0)]],
//       hssL_MediumOrderMultiplier: [1.0, [Validators.required, Validators.min(0)]],
//       hssL_HeavyOrderMultiplier: [2.0, [Validators.required, Validators.min(0)]],

//       // Penalty Configuration
//       penalty_HeavyError: [500, [Validators.required, Validators.min(0)]],
//       penalty_LightError: [100, [Validators.required, Validators.min(0)]],
//       penalty_NoError: [0, [Validators.required, Validators.min(0)]],

//       // Reward/Penalty Thresholds
//       reward_HighPerformance_MinStars: [3.4, [Validators.required, Validators.min(0), Validators.max(5)]],
//       reward_HighPerformance_MaxStars: [5.0, [Validators.required, Validators.min(0), Validators.max(5)]],
//       reward_BaseAmount: [2500000, [Validators.required, Validators.min(0)]],
//       reward_BasePenalty: [500000, [Validators.required, Validators.min(0)]],
//       penalty_MediumPerformance_MinStars: [3.0, [Validators.required, Validators.min(0), Validators.max(5)]],
//       penalty_MediumPerformance_MaxStars: [3.4, [Validators.required, Validators.min(0), Validators.max(5)]],
//       penalty_LowPerformance_MinStars: [1.0, [Validators.required, Validators.min(0), Validators.max(5)]],
//       penalty_LowPerformance_MaxStars: [3.0, [Validators.required, Validators.min(0), Validators.max(5)]],
//       penalty_LowPerformance_BaseAmount: [500000, [Validators.required, Validators.min(0)]],
//       penalty_LowPerformance_MaxPenalty: [1000000, [Validators.required, Validators.min(0)]],

//       description: ['', Validators.required]
//     });
//   }

//   private loadActiveConfiguration(): void {
//     this.isLoading = true;
    
//     const subscription = this.repoService.getData('api/kpi-configuration/active')
//       .subscribe({
//         next: (response: any) => {
//           this.currentConfig = response as KpiConfiguration;
//           this.kpiForm.patchValue(this.currentConfig);
//           this.isLoading = false;
//         },
//         error: (err: any) => {
//           console.error('Error loading KPI configuration:', err);
//           this.toastr.error('Không thể tải cấu hình KPI');
//           this.isLoading = false;
//         }
//       });
    
//     this.subscriptions.add(subscription);
//   }

//   public onSubmit(): void {
//     if (this.kpiForm.invalid) {
//       this.markFormGroupTouched();
//       this.toastr.warning('Vui lòng kiểm tra lại các trường dữ liệu');
//       return;
//     }

//     const formValue = this.kpiForm.value;
//     this.isLoading = true;

//     if (this.currentConfig) {
//       // Update existing configuration
//       const subscription = this.repoService.update(`api/kpi-configuration/${this.currentConfig.id}`, formValue)
//         .subscribe({
//           next: (response: any) => {
//             this.toastr.success('Cập nhật cấu hình KPI thành công');
//             this.loadActiveConfiguration();
//           },
//           error: (err: any) => {
//             console.error('Error updating KPI configuration:', err);
//             this.toastr.error('Có lỗi xảy ra khi cập nhật cấu hình KPI');
//             this.isLoading = false;
//           }
//         });
      
//       this.subscriptions.add(subscription);
//     } else {
//       // Create new configuration
//       const subscription = this.repoService.create('api/kpi-configuration', formValue)
//         .subscribe({
//           next: (response: any) => {
//             this.toastr.success('Tạo cấu hình KPI thành công');
//             this.loadActiveConfiguration();
//           },
//           error: (err: any) => {
//             console.error('Error creating KPI configuration:', err);
//             this.toastr.error('Có lỗi xảy ra khi tạo cấu hình KPI');
//             this.isLoading = false;
//           }
//         });
      
//       this.subscriptions.add(subscription);
//     }
//   }

//   public resetForm(): void {
//     if (this.currentConfig) {
//       this.kpiForm.patchValue(this.currentConfig);
//     } else {
//       this.kpiForm.reset();
//       this.initializeForm();
//     }
//     this.toastr.info('Đã khôi phục dữ liệu ban đầu');
//   }

//   public createDefaultConfiguration(): void {
//   const confirmed = confirm('Bạn có chắc chắn muốn tạo cấu hình KPI mặc định? Thao tác này sẽ ghi đè cấu hình hiện tại.');
  
//   if (confirmed) {
//     const subscription = this.repoService.create('api/kpi-configuration/create-default', {})
//       .subscribe({
//         next: (response: any) => {
//           this.toastr.success('Tạo cấu hình mặc định thành công');
//           this.loadActiveConfiguration();
//         },
//         error: (err: any) => {
//           console.error('Error creating default configuration:', err);
//           this.toastr.error('Có lỗi xảy ra khi tạo cấu hình mặc định');
//         }
//       });
    
//     this.subscriptions.add(subscription);
//   }
// }

//   private markFormGroupTouched(): void {
//     Object.keys(this.kpiForm.controls).forEach(key => {
//       const control = this.kpiForm.get(key);
//       control?.markAsTouched();
//     });
//   }

//   public getFieldError(fieldName: string): string {
//     const control = this.kpiForm.get(fieldName);
//     if (control && control.errors && control.touched) {
//       if (control.errors['required']) return `${fieldName} là bắt buộc`;
//       if (control.errors['min']) return `Giá trị phải lớn hơn hoặc bằng ${control.errors['min'].min}`;
//       if (control.errors['max']) return `Giá trị phải nhỏ hơn hoặc bằng ${control.errors['max'].max}`;
//     }
//     return '';
//   }

//   public formatCurrency(amount: number): string {
//     return new Intl.NumberFormat('vi-VN', { 
//       style: 'currency', 
//       currency: 'VND',
//       minimumFractionDigits: 0,
//       maximumFractionDigits: 0
//     }).format(amount);
//   }

//   // Example calculation methods for display
//   public calculateStarsExample(): string {
//     const early = this.kpiForm.get('stars_EarlyCompletion')?.value || 5;
//     const onTime = this.kpiForm.get('stars_OnTime')?.value || 4;
//     const late1 = this.kpiForm.get('stars_Late1Day')?.value || 3;
    
//     return `Hoàn thành sớm: ${early}★, Đúng hạn: ${onTime}★, Trễ 1 ngày: ${late1}★`;
//   }
// }
import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import { RepositoryService } from 'src/app/shared/services/repository.service';
import { ToastrService } from 'ngx-toastr';
import { DialogService } from 'src/app/shared/services/dialog.service';
import { Subscription } from 'rxjs';

export interface KpiConfiguration {
  id: number;
  stars_EarlyCompletion: number;
  stars_OnTime: number;
  stars_Late1Day: number;
  stars_Late2Days: number;
  stars_Late3OrMoreDays: number;
  lightOrder_MaxDays: number;
  mediumOrder_MinDays: number;
  mediumOrder_MaxDays: number;
  heavyOrder_MinDays: number;
  hssL_LightOrderFreeCount: number;
  hssL_LightOrderMultiplier: number;
  hssL_MediumOrderMultiplier: number;
  hssL_HeavyOrderMultiplier: number;
  penalty_HeavyError: number;
  penalty_LightError: number;
  penalty_NoError: number;
  reward_HighPerformance_MinStars: number;
  reward_HighPerformance_MaxStars: number;
  reward_BaseAmount: number;
  reward_BasePenalty: number;
  penalty_MediumPerformance_MinStars: number;
  penalty_MediumPerformance_MaxStars: number;
  penalty_LowPerformance_MinStars: number;
  penalty_LowPerformance_MaxStars: number;
  penalty_LowPerformance_BaseAmount: number;
  penalty_LowPerformance_MaxPenalty: number;
  createdAt: string;
  updatedAt?: string;
  description: string;
  isActive: boolean;
}

@Component({
  selector: 'app-kpi-settings',
  templateUrl: './kpi-settings.component.html',
  styleUrls: ['./kpi-settings.component.scss']
})
export class KpiSettingsComponent implements OnInit, OnDestroy {
  kpiForm!: FormGroup;
  currentConfig?: KpiConfiguration;
  isLoading = false;
  
  private subscriptions = new Subscription();

  constructor(
    private fb: FormBuilder,
    private repoService: RepositoryService,
    private toastr: ToastrService,
    private dialogService: DialogService
  ) { }

  ngOnInit(): void {
    this.initializeForm();
    this.setupFormValidations();
    this.loadActiveConfiguration();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  // ============= CUSTOM VALIDATORS =============
  
  /**
   * Validator để đảm bảo stars giảm dần theo thứ tự: Sớm > Đúng hạn > Trễ 1 > Trễ 2 > Trễ 3+
   */
  private starsSequenceValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.parent) return null;

    const early = control.parent.get('stars_EarlyCompletion')?.value;
    const onTime = control.parent.get('stars_OnTime')?.value;
    const late1 = control.parent.get('stars_Late1Day')?.value;
    const late2 = control.parent.get('stars_Late2Days')?.value;
    const late3 = control.parent.get('stars_Late3OrMoreDays')?.value;

    const errors: ValidationErrors = {};

    // Kiểm tra thứ tự giảm dần
    if (early && onTime && early <= onTime) {
      errors['starsSequence'] = 'Sao hoàn thành sớm phải lớn hơn sao đúng hạn';
    }
    if (onTime && late1 && onTime <= late1) {
      errors['starsSequence'] = 'Sao đúng hạn phải lớn hơn sao trễ 1 ngày';
    }
    if (late1 && late2 && late1 <= late2) {
      errors['starsSequence'] = 'Sao trễ 1 ngày phải lớn hơn sao trễ 2 ngày';
    }
    if (late2 && late3 && late2 <= late3) {
      errors['starsSequence'] = 'Sao trễ 2 ngày phải lớn hơn sao trễ 3+ ngày';
    }

    return Object.keys(errors).length > 0 ? errors : null;
  }

  /**
   * Validator để đảm bảo thứ tự đơn hàng hợp lý
   */
  private orderSequenceValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.parent) return null;

    const lightMax = control.parent.get('lightOrder_MaxDays')?.value;
    const mediumMin = control.parent.get('mediumOrder_MinDays')?.value;
    const mediumMax = control.parent.get('mediumOrder_MaxDays')?.value;
    const heavyMin = control.parent.get('heavyOrder_MinDays')?.value;

    const errors: ValidationErrors = {};

    // Đơn vừa tối thiểu phải >= đơn nhỏ tối đa
    if (lightMax && mediumMin && mediumMin < lightMax) {
      errors['orderSequence'] = `Đơn vừa tối thiểu phải >= đơn nhỏ tối đa (${lightMax})`;
    }

    // Đơn vừa tối đa phải >= đơn vừa tối thiểu
    if (mediumMin && mediumMax && mediumMax < mediumMin) {
      errors['orderSequence'] = 'Đơn vừa tối đa phải >= đơn vừa tối thiểu';
    }

    // Đơn lớn tối thiểu phải > đơn vừa tối đa
    if (mediumMax && heavyMin && heavyMin <= mediumMax) {
      errors['orderSequence'] = `Đơn lớn tối thiểu phải > đơn vừa tối đa (${mediumMax})`;
    }

    return Object.keys(errors).length > 0 ? errors : null;
  }

  /**
   * Validator để đảm bảo thứ tự thưởng/phạt hợp lý
   */
  private rewardSequenceValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.parent) return null;

    const highMin = control.parent.get('reward_HighPerformance_MinStars')?.value;
    const highMax = control.parent.get('reward_HighPerformance_MaxStars')?.value;
    const mediumMin = control.parent.get('penalty_MediumPerformance_MinStars')?.value;
    const mediumMax = control.parent.get('penalty_MediumPerformance_MaxStars')?.value;
    const lowMin = control.parent.get('penalty_LowPerformance_MinStars')?.value;
    const lowMax = control.parent.get('penalty_LowPerformance_MaxStars')?.value;

    const errors: ValidationErrors = {};

    // High performance: Min <= Max
    if (highMin && highMax && highMin > highMax) {
      errors['rewardSequence'] = 'Sao tối thiểu phải <= sao tối đa (hiệu suất cao)';
    }

    // Medium performance: Min <= Max
    if (mediumMin && mediumMax && mediumMin > mediumMax) {
      errors['rewardSequence'] = 'Sao tối thiểu phải <= sao tối đa (hiệu suất trung bình)';
    }

    // Low performance: Min <= Max
    if (lowMin && lowMax && lowMin > lowMax) {
      errors['rewardSequence'] = 'Sao tối thiểu phải <= sao tối đa (hiệu suất thấp)';
    }

    // Các khoảng không được chồng lấn: Low < Medium < High
    if (lowMax && mediumMin && lowMax >= mediumMin) {
      errors['rewardSequence'] = 'Hiệu suất thấp tối đa phải < hiệu suất trung bình tối thiểu';
    }

    if (mediumMax && highMin && mediumMax >= highMin) {
      errors['rewardSequence'] = 'Hiệu suất trung bình tối đa phải < hiệu suất cao tối thiểu';
    }

    return Object.keys(errors).length > 0 ? errors : null;
  }

  private initializeForm(): void {
    this.kpiForm = this.fb.group({
      // Stars Configuration - với validation sequence
      stars_EarlyCompletion: [5, [Validators.required, Validators.min(1), Validators.max(5), this.starsSequenceValidator.bind(this)]],
      stars_OnTime: [4, [Validators.required, Validators.min(1), Validators.max(5), this.starsSequenceValidator.bind(this)]],
      stars_Late1Day: [3, [Validators.required, Validators.min(1), Validators.max(5), this.starsSequenceValidator.bind(this)]],
      stars_Late2Days: [2, [Validators.required, Validators.min(1), Validators.max(5), this.starsSequenceValidator.bind(this)]],
      stars_Late3OrMoreDays: [1, [Validators.required, Validators.min(1), Validators.max(5), this.starsSequenceValidator.bind(this)]],

      // Order Categorization - với validation sequence
      lightOrder_MaxDays: [5, [Validators.required, Validators.min(1), this.orderSequenceValidator.bind(this)]],
      mediumOrder_MinDays: [5, [Validators.required, Validators.min(1), this.orderSequenceValidator.bind(this)]],
      mediumOrder_MaxDays: [19, [Validators.required, Validators.min(1), this.orderSequenceValidator.bind(this)]],
      heavyOrder_MinDays: [20, [Validators.required, Validators.min(1), this.orderSequenceValidator.bind(this)]],

      // HSSL Configuration
      hssL_LightOrderFreeCount: [5, [Validators.required, Validators.min(0)]],
      hssL_LightOrderMultiplier: [0.1, [Validators.required, Validators.min(0)]],
      hssL_MediumOrderMultiplier: [1.0, [Validators.required, Validators.min(0)]],
      hssL_HeavyOrderMultiplier: [2.0, [Validators.required, Validators.min(0)]],

      // Penalty Configuration
      penalty_HeavyError: [500, [Validators.required, Validators.min(0)]],
      penalty_LightError: [100, [Validators.required, Validators.min(0)]],
      penalty_NoError: [0, [Validators.required, Validators.min(0)]],

      // Reward/Penalty Thresholds - với validation sequence
      reward_HighPerformance_MinStars: [3.4, [Validators.required, Validators.min(0), Validators.max(5), this.rewardSequenceValidator.bind(this)]],
      reward_HighPerformance_MaxStars: [5.0, [Validators.required, Validators.min(0), Validators.max(5), this.rewardSequenceValidator.bind(this)]],
      reward_BaseAmount: [2500000, [Validators.required, Validators.min(0)]],
      reward_BasePenalty: [500000, [Validators.required, Validators.min(0)]],
      penalty_MediumPerformance_MinStars: [3.0, [Validators.required, Validators.min(0), Validators.max(5), this.rewardSequenceValidator.bind(this)]],
      penalty_MediumPerformance_MaxStars: [3.4, [Validators.required, Validators.min(0), Validators.max(5), this.rewardSequenceValidator.bind(this)]],
      penalty_LowPerformance_MinStars: [1.0, [Validators.required, Validators.min(0), Validators.max(5), this.rewardSequenceValidator.bind(this)]],
      penalty_LowPerformance_MaxStars: [3.0, [Validators.required, Validators.min(0), Validators.max(5), this.rewardSequenceValidator.bind(this)]],
      penalty_LowPerformance_BaseAmount: [500000, [Validators.required, Validators.min(0)]],
      penalty_LowPerformance_MaxPenalty: [1000000, [Validators.required, Validators.min(0)]],

      description: ['', Validators.required]
    });
  }

  private setupFormValidations(): void {
    // ============= AUTO-UPDATE LOGIC FOR ORDER FIELDS =============
    
    // Khi lightOrder_MaxDays thay đổi → tự động cập nhật mediumOrder_MinDays = lightOrder_MaxDays
    this.kpiForm.get('lightOrder_MaxDays')?.valueChanges.subscribe(value => {
      if (value && value > 0) {
        const mediumMinControl = this.kpiForm.get('mediumOrder_MinDays');
        const currentMediumMin = mediumMinControl?.value;
        
        // Tạm thời disable validator để tránh conflict
        setTimeout(() => {
          if (currentMediumMin !== value) {
            mediumMinControl?.setValue(value, { emitEvent: false });
            // Clear errors sau khi update
            mediumMinControl?.markAsUntouched();
            mediumMinControl?.updateValueAndValidity({ emitEvent: false });
            this.toastr.info(`Đã tự động cập nhật đơn vừa tối thiểu thành ${value}`);
          }
        }, 10);
      }
    });

    // Khi mediumOrder_MinDays thay đổi → tự động cập nhật lightOrder_MaxDays = mediumOrder_MinDays
    this.kpiForm.get('mediumOrder_MinDays')?.valueChanges.subscribe(value => {
      if (value && value > 0) {
        const lightMaxControl = this.kpiForm.get('lightOrder_MaxDays');
        const currentLightMax = lightMaxControl?.value;
        
        setTimeout(() => {
          if (currentLightMax !== value) {
            lightMaxControl?.setValue(value, { emitEvent: false });
            // Clear errors sau khi update
            lightMaxControl?.markAsUntouched();
            lightMaxControl?.updateValueAndValidity({ emitEvent: false });
            this.toastr.info(`Đã tự động cập nhật đơn nhỏ tối đa thành ${value}`);
          }
        }, 10);
      }
    });

    // Khi mediumOrder_MaxDays thay đổi → tự động cập nhật heavyOrder_MinDays = mediumOrder_MaxDays + 1
    this.kpiForm.get('mediumOrder_MaxDays')?.valueChanges.subscribe(value => {
      if (value && value > 0) {
        const heavyMinControl = this.kpiForm.get('heavyOrder_MinDays');
        const newHeavyMin = value + 1;
        const currentHeavyMin = heavyMinControl?.value;
        
        setTimeout(() => {
          if (currentHeavyMin !== newHeavyMin) {
            heavyMinControl?.setValue(newHeavyMin, { emitEvent: false });
            heavyMinControl?.markAsUntouched();
            heavyMinControl?.updateValueAndValidity({ emitEvent: false });
            this.toastr.info(`Đã tự động cập nhật đơn lớn tối thiểu thành ${newHeavyMin}`);
          }
        }, 10);
      }
    });

    // Khi heavyOrder_MinDays thay đổi → tự động cập nhật mediumOrder_MaxDays = heavyOrder_MinDays - 1
    this.kpiForm.get('heavyOrder_MinDays')?.valueChanges.subscribe(value => {
      if (value && value > 1) {
        const mediumMaxControl = this.kpiForm.get('mediumOrder_MaxDays');
        const newMediumMax = value - 1;
        const currentMediumMax = mediumMaxControl?.value;
        
        setTimeout(() => {
          if (currentMediumMax !== newMediumMax && newMediumMax > 0) {
            mediumMaxControl?.setValue(newMediumMax, { emitEvent: false });
            mediumMaxControl?.markAsUntouched();
            mediumMaxControl?.updateValueAndValidity({ emitEvent: false });
            this.toastr.info(`Đã tự động cập nhật đơn vừa tối đa thành ${newMediumMax}`);
            
            // Kiểm tra và cập nhật mediumOrder_MinDays nếu cần
            const mediumMinControl = this.kpiForm.get('mediumOrder_MinDays');
            const currentMediumMin = mediumMinControl?.value;
            if (currentMediumMin && currentMediumMin > newMediumMax) {
              setTimeout(() => {
                mediumMinControl?.setValue(newMediumMax, { emitEvent: false });
                mediumMinControl?.markAsUntouched();
                mediumMinControl?.updateValueAndValidity({ emitEvent: false });
                this.toastr.info(`Đã điều chỉnh đơn vừa tối thiểu thành ${newMediumMax}`);
                
                // Cập nhật luôn lightOrder_MaxDays
                const lightMaxControl = this.kpiForm.get('lightOrder_MaxDays');
                setTimeout(() => {
                  lightMaxControl?.setValue(newMediumMax, { emitEvent: false });
                  lightMaxControl?.markAsUntouched();
                  lightMaxControl?.updateValueAndValidity({ emitEvent: false });
                  this.toastr.info(`Đã điều chỉnh đơn nhỏ tối đa thành ${newMediumMax}`);
                }, 10);
              }, 10);
            }
          }
        }, 10);
      }
    });

    // ============= CROSS-FIELD VALIDATION =============
    
    // Khi bất kỳ stars field nào thay đổi → revalidate tất cả stars fields
    const starsFields = ['stars_EarlyCompletion', 'stars_OnTime', 'stars_Late1Day', 'stars_Late2Days', 'stars_Late3OrMoreDays'];
    starsFields.forEach(field => {
      this.kpiForm.get(field)?.valueChanges.subscribe(() => {
        setTimeout(() => {
          starsFields.forEach(f => {
            if (f !== field) {
              this.kpiForm.get(f)?.updateValueAndValidity({ emitEvent: false });
            }
          });
        }, 50);
      });
    });

    // Khi bất kỳ order field nào thay đổi → revalidate tất cả order fields
    const orderFields = ['lightOrder_MaxDays', 'mediumOrder_MinDays', 'mediumOrder_MaxDays', 'heavyOrder_MinDays'];
    orderFields.forEach(field => {
      this.kpiForm.get(field)?.valueChanges.subscribe(() => {
        // Delay nhiều hơn để auto-update hoàn thành
        setTimeout(() => {
          orderFields.forEach(f => {
            this.kpiForm.get(f)?.updateValueAndValidity({ emitEvent: false });
          });
        }, 100);
      });
    });

    // Khi bất kỳ reward field nào thay đổi → revalidate tất cả reward fields
    const rewardFields = [
      'reward_HighPerformance_MinStars', 'reward_HighPerformance_MaxStars',
      'penalty_MediumPerformance_MinStars', 'penalty_MediumPerformance_MaxStars',
      'penalty_LowPerformance_MinStars', 'penalty_LowPerformance_MaxStars'
    ];
    rewardFields.forEach(field => {
      this.kpiForm.get(field)?.valueChanges.subscribe(() => {
        setTimeout(() => {
          rewardFields.forEach(f => {
            if (f !== field) {
              this.kpiForm.get(f)?.updateValueAndValidity({ emitEvent: false });
            }
          });
        }, 50);
      });
    });
  }

  private loadActiveConfiguration(): void {
    this.isLoading = true;
    
    const subscription = this.repoService.getData('api/kpi-configuration/active')
      .subscribe({
        next: (response: any) => {
          this.currentConfig = response as KpiConfiguration;
          this.kpiForm.patchValue(this.currentConfig);
          this.isLoading = false;
        },
        error: (err: any) => {
          console.error('Error loading KPI configuration:', err);
          this.toastr.error('Không thể tải cấu hình KPI');
          this.isLoading = false;
        }
      });
    
    this.subscriptions.add(subscription);
  }

  public onSubmit(): void {
    if (this.kpiForm.invalid) {
      this.markFormGroupTouched();
      this.showFormErrors();
      return;
    }

    const formValue = this.kpiForm.value;
    this.isLoading = true;

    if (this.currentConfig) {
      // Update existing configuration
      const subscription = this.repoService.update(`api/kpi-configuration/${this.currentConfig.id}`, formValue)
        .subscribe({
          next: (response: any) => {
            this.toastr.success('Cập nhật cấu hình KPI thành công');
            this.loadActiveConfiguration();
          },
          error: (err: any) => {
            console.error('Error updating KPI configuration:', err);
            this.toastr.error('Có lỗi xảy ra khi cập nhật cấu hình KPI');
            this.isLoading = false;
          }
        });
      
      this.subscriptions.add(subscription);
    } else {
      // Create new configuration
      const subscription = this.repoService.create('api/kpi-configuration', formValue)
        .subscribe({
          next: (response: any) => {
            this.toastr.success('Tạo cấu hình KPI thành công');
            this.loadActiveConfiguration();
          },
          error: (err: any) => {
            console.error('Error creating KPI configuration:', err);
            this.toastr.error('Có lỗi xảy ra khi tạo cấu hình KPI');
            this.isLoading = false;
          }
        });
      
      this.subscriptions.add(subscription);
    }
  }

  public resetForm(): void {
    if (this.currentConfig) {
      this.kpiForm.patchValue(this.currentConfig);
    } else {
      this.kpiForm.reset();
      this.initializeForm();
    }
    this.toastr.info('Đã khôi phục dữ liệu ban đầu');
  }

  public createDefaultConfiguration(): void {
    const confirmed = confirm('Bạn có chắc chắn muốn tạo cấu hình KPI mặc định? Thao tác này sẽ ghi đè cấu hình hiện tại.');
    
    if (confirmed) {
      const subscription = this.repoService.create('api/kpi-configuration/create-default', {})
        .subscribe({
          next: (response: any) => {
            this.toastr.success('Tạo cấu hình mặc định thành công');
            this.loadActiveConfiguration();
          },
          error: (err: any) => {
            console.error('Error creating default configuration:', err);
            this.toastr.error('Có lỗi xảy ra khi tạo cấu hình mặc định');
          }
        });
      
      this.subscriptions.add(subscription);
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.kpiForm.controls).forEach(key => {
      const control = this.kpiForm.get(key);
      control?.markAsTouched();
    });
  }

  private showFormErrors(): void {
    const errorMessages: string[] = [];
    
    Object.keys(this.kpiForm.controls).forEach(key => {
      const control = this.kpiForm.get(key);
      if (control && control.errors && control.touched) {
        const fieldError = this.getFieldError(key);
        if (fieldError) {
          errorMessages.push(fieldError);
        }
      }
    });

    if (errorMessages.length > 0) {
      this.toastr.warning(`Vui lòng kiểm tra lại:\n${errorMessages.slice(0, 3).join('\n')}${errorMessages.length > 3 ? '\n...' : ''}`);
    }
  }

  public getFieldError(fieldName: string): string {
    const control = this.kpiForm.get(fieldName);
    if (control && control.errors && control.touched) {
      // Custom sequence validation errors
      if (control.errors['starsSequence']) return control.errors['starsSequence'];
      if (control.errors['orderSequence']) return control.errors['orderSequence'];
      if (control.errors['rewardSequence']) return control.errors['rewardSequence'];
      
      // Standard validation errors
      if (control.errors['required']) return `${this.getFieldDisplayName(fieldName)} là bắt buộc`;
      if (control.errors['min']) return `Giá trị phải lớn hơn hoặc bằng ${control.errors['min'].min}`;
      if (control.errors['max']) return `Giá trị phải nhỏ hơn hoặc bằng ${control.errors['max'].max}`;
    }
    return '';
  }

  private getFieldDisplayName(fieldName: string): string {
    const displayNames: { [key: string]: string } = {
      'stars_EarlyCompletion': 'Sao hoàn thành sớm',
      'stars_OnTime': 'Sao đúng hạn',
      'stars_Late1Day': 'Sao trễ 1 ngày',
      'stars_Late2Days': 'Sao trễ 2 ngày',
      'stars_Late3OrMoreDays': 'Sao trễ 3+ ngày',
      'lightOrder_MaxDays': 'Đơn nhỏ tối đa',
      'mediumOrder_MinDays': 'Đơn vừa tối thiểu',
      'mediumOrder_MaxDays': 'Đơn vừa tối đa',
      'heavyOrder_MinDays': 'Đơn lớn tối thiểu',
      'description': 'Mô tả'
    };
    return displayNames[fieldName] || fieldName;
  }

  public formatCurrency(amount: number): string {
    return new Intl.NumberFormat('vi-VN', { 
      style: 'currency', 
      currency: 'VND',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }

  // ============= HELPER METHODS FOR DISPLAY =============
  
  public calculateStarsExample(): string {
    const early = this.kpiForm.get('stars_EarlyCompletion')?.value || 5;
    const onTime = this.kpiForm.get('stars_OnTime')?.value || 4;
    const late1 = this.kpiForm.get('stars_Late1Day')?.value || 3;
    
    return `Ví dụ: Hoàn thành sớm: ${early}★, Đúng hạn: ${onTime}★, Trễ 1 ngày: ${late1}★`;
  }

  public getOrderCategoryExample(): string {
    const lightMax = this.kpiForm.get('lightOrder_MaxDays')?.value || 5;
    const mediumMin = this.kpiForm.get('mediumOrder_MinDays')?.value || 5;
    const mediumMax = this.kpiForm.get('mediumOrder_MaxDays')?.value || 19;
    const heavyMin = this.kpiForm.get('heavyOrder_MinDays')?.value || 20;
    
    return `Ví dụ: Đơn nhỏ < ${lightMax} ngày, Đơn vừa ${mediumMin}-${mediumMax} ngày, Đơn lớn ≥ ${heavyMin} ngày`;
  }

  // ============= VALIDATION ERROR CHECKERS =============

  public hasStarsValidationErrors(): boolean {
    const starsFields = ['stars_EarlyCompletion', 'stars_OnTime', 'stars_Late1Day', 'stars_Late2Days', 'stars_Late3OrMoreDays'];
    return starsFields.some(field => {
      const control = this.kpiForm.get(field);
      return control && control.errors && control.errors['starsSequence'] && control.touched;
    });
  }

  public hasOrderValidationErrors(): boolean {
    const orderFields = ['lightOrder_MaxDays', 'mediumOrder_MinDays', 'mediumOrder_MaxDays', 'heavyOrder_MinDays'];
    return orderFields.some(field => {
      const control = this.kpiForm.get(field);
      return control && control.errors && control.errors['orderSequence'] && control.touched;
    });
  }

  public hasRewardValidationErrors(): boolean {
    const rewardFields = [
      'reward_HighPerformance_MinStars', 'reward_HighPerformance_MaxStars',
      'penalty_MediumPerformance_MinStars', 'penalty_MediumPerformance_MaxStars',
      'penalty_LowPerformance_MinStars', 'penalty_LowPerformance_MaxStars'
    ];
    return rewardFields.some(field => {
      const control = this.kpiForm.get(field);
      return control && control.errors && control.errors['rewardSequence'] && control.touched;
    });
  }

  public getValidationSummary(): string[] {
    const errors: string[] = [];
    
    if (this.hasStarsValidationErrors()) {
      errors.push('Số sao phải theo thứ tự giảm dần');
    }
    
    if (this.hasOrderValidationErrors()) {
      errors.push('Phân loại đơn hàng chưa hợp lý');
    }
    
    if (this.hasRewardValidationErrors()) {
      errors.push('Khoảng sao thưởng/phạt bị chồng lấn');
    }
    
    return errors;
  }
}