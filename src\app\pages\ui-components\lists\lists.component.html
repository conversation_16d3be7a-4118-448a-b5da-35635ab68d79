<mat-card class="cardWithShadow theme-card">
  <mat-card-header>
    <mat-card-title class="m-b-0">Customers</mat-card-title>
    <span class="flex-1-auto"></span>

    <button mat-flat-button color="primary" matTooltipPosition="left" class="m-l-8" matTooltipHideDelay="100000"
      (click)="addCustomer()">
      <mat-icon>add</mat-icon>  <span fxHide.xs>Create new customer</span>
    </button>
  </mat-card-header>

  <mat-card-content class="b-t-1">
    <mat-form-field class="w-100" appearance="outline">
      <input matInput type="text" (keyup)="doFilter($any($event).target.value)" placeholder="Search....">
    </mat-form-field>

    <div class="table-responsive m-t-16">
      <table mat-table [dataSource]="dataSource" class="w-100" matSort>

        <ng-container matColumnDef="action">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14">
            Action
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            <button mat-flat-button color="primary" [matMenuTriggerFor]="actions" class="m-t-8">
              Action <mat-icon>arrow_drop_down</mat-icon>
            </button>
            <mat-menu class="cardWithShadow" #actions="matMenu">
              <button mat-menu-item (click)="redirectToDetails(element.id)">
                Details
              </button>
              <button mat-menu-item (click)="updateCustomer(element.id)">
                Edit
              </button>
              <button mat-menu-item (click)="DeleteCustomer(element.id)">
                Delete
              </button>
            </mat-menu>
          </td>
        </ng-container>

        <ng-container matColumnDef="firstName">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14" mat-sort-header>
            FirstName
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            {{ element.firstName }}
          </td>
        </ng-container>


        <ng-container matColumnDef="lastName">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14" mat-sort-header>
            LastName
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            {{ element.lastName }}
          </td>
        </ng-container>

        <ng-container matColumnDef="dateOfBirth">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14" mat-sort-header>
            DateOfBirth
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            {{ element.dateOfBirth | date: 'mediumDate'}}
          </td>
        </ng-container>

        <ng-container matColumnDef="country">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14" mat-sort-header>
            Country
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            {{ element.country }}
          </td>
        </ng-container>

        <ng-container matColumnDef="type">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14">
            Type
          </th>
          <td mat-cell *matCellDef="let element">
            <ng-template [ngIf]="element.accountCount == '0'">
              <span class="bg-light-warning text-warning rounded f-w-600 p-6 p-y-4 f-s-12">
                No Account
              </span>
            </ng-template>
            <ng-template [ngIf]="element.accountCount == '1'">
              <span class="bg-light-accent text-accent rounded f-w-600 p-6 p-y-4 f-s-12">
                Beginner
              </span>
            </ng-template>
            <ng-template [ngIf]="element.accountCount == '2'">
              <span class="bg-light-primary text-primary rounded f-w-600 p-6 p-y-4 f-s-12">
                Advanced
              </span>
            </ng-template>
            <ng-template [ngIf]="element.accountCount == '3'">
              <span class="bg-light-warning text-warning rounded f-w-600 p-6 p-y-4 f-s-12">
                Gold
              </span>
            </ng-template>
            <ng-template [ngIf]="+(element.accountCount) >= 4">
              <span class="bg-light-success text-success rounded f-w-600 p-6 p-y-4 f-s-12">
                Platinum
              </span>
            </ng-template>

          </td>
        </ng-container>

        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14">
            Status
          </th>
          <td mat-cell *matCellDef="let element">
            <ng-template [ngIf]="element.status == true">
              <span class="bg-light-accent text-accent rounded f-w-600 p-6 p-y-4 f-s-12">
                Active
              </span>
            </ng-template>
            <ng-template [ngIf]="element.status == false">
              <span class="bg-light-warning text-warning rounded f-w-600 p-6 p-y-4 f-s-12">
                Blocked
              </span>
            </ng-template>

          </td>
        </ng-container>

        <ng-container matColumnDef="Details">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14">
            Details
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            <mat-icon color="primary">remove_red_eye</mat-icon>
          </td>
        </ng-container>



        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
    </div>


    <mat-paginator [pageSize]="4" [pageSizeOptions]="[4, 6, 10, 20]">
    </mat-paginator>
  </mat-card-content>
</mat-card>