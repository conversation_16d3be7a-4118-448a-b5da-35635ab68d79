import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MaterialModule } from '../../material.module';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TablerIconsModule } from 'angular-tabler-icons';
import * as TablerIcons from 'angular-tabler-icons/icons';
import { UiComponentsRoutes } from './ui-components.routing';
import { AppListsComponent } from './lists/lists.component';
import { MatNativeDateModule } from '@angular/material/core';
import { UsersComponent } from './users/users.component';
import { RolesComponent } from './roles/roles.component';
import { AuditsComponent } from './audits/audits.component';
import { ToastrModule } from 'ngx-toastr';
import { AddCustomerComponent } from './lists/add-customer/add-customer.component';
import { UpdateCustomerComponent } from './lists/update-customer/update-customer.component';
import { DetailsComponent } from './lists/details/details.component';
import { AddAccountComponent } from './lists/add-account/add-account.component';
import { AddUserComponent } from './users/add-user/add-user.component';
import { UpdateUserComponent } from './users/update-user/update-user.component';
import { AddRoleComponent } from './roles/add-role/add-role.component';
import { UpdateRoleComponent } from './roles/update-role/update-role.component';
import { CategoriesComponent } from './categories/categories.component';
import { AddCategoryComponent } from './categories/add-category/add-category.component';
import { UpdateCategoryComponent } from './categories/update-category/update-category.component';
import { PermissionsComponent } from './permissions/permissions.component';
import { AddPermissionComponent } from './permissions/add-permission/add-permission.component';
import { UpdatePermissionComponent } from './permissions/update-permission/update-permission.component';
import { AssignPermissionsComponent } from './roles/assign-permissions/assign-permissions.component';
import { UserCalendarsComponent } from './user-calendars/user-calendars.component';
import { AddUserCalendarComponent } from './user-calendars/add-user-calendar/add-user-calendar.component';
import { UpdateUserCalendarComponent } from './user-calendars/update-user-calendar/update-user-calendar.component';
import { KpiReportComponent } from './kpi-report/kpi-report.component';
import { KpiSettingsComponent } from './kpi-settings/kpi-settings.component';
import { EventExclusionKeywordsComponent } from './event-exclusion-keywords/event-exclusion-keywords.component';
import { AddEventExclusionKeywordComponent } from './event-exclusion-keywords/add-event-exclusion-keyword/add-event-exclusion-keyword.component';
import { UpdateEventExclusionKeywordComponent } from './event-exclusion-keywords/update-event-exclusion-keyword/update-event-exclusion-keyword.component';
import { EmailCcConfigurationsComponent } from './email-cc-configurations/email-cc-configurations.component';
import { AddEmailCcConfigurationComponent } from './email-cc-configurations/add-email-cc-configuration/add-email-cc-configuration.component';
import { UpdateEmailCcConfigurationComponent } from './email-cc-configurations/update-email-cc-configuration/update-email-cc-configuration.component';

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(UiComponentsRoutes),
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    TablerIconsModule.pick(TablerIcons),
    MatNativeDateModule,
    ToastrModule.forRoot({
      timeOut: 3000, 
      positionClass: 'toast-top-right', 
      preventDuplicates: true, 
      progressBar: true, 
      closeButton: true 
    })
  ],
  declarations: [
    AppListsComponent,
    UsersComponent,
    RolesComponent,
    AuditsComponent,
    AddCustomerComponent,
    UpdateCustomerComponent,
    DetailsComponent,
    AddAccountComponent,
    AddUserComponent,
    UpdateUserComponent,
    AddRoleComponent,
    UpdateRoleComponent,
    CategoriesComponent,
    AddCategoryComponent,
    UpdateCategoryComponent,
    PermissionsComponent,
    AddPermissionComponent,
    UpdatePermissionComponent,
    AssignPermissionsComponent,
    UserCalendarsComponent,
    AddUserCalendarComponent,
    UpdateUserCalendarComponent,
    KpiReportComponent,
    KpiSettingsComponent,
    EventExclusionKeywordsComponent,
    AddEventExclusionKeywordComponent,
    UpdateEventExclusionKeywordComponent,
    EmailCcConfigurationsComponent,
    AddEmailCcConfigurationComponent,
    UpdateEmailCcConfigurationComponent,
  ],
})
export class UicomponentsModule {}




