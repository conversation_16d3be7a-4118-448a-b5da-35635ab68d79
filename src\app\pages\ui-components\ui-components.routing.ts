import { Routes } from '@angular/router';
import { AppListsComponent } from './lists/lists.component';
import { UsersComponent } from './users/users.component';
import { RolesComponent } from './roles/roles.component';
import { AuditsComponent } from './audits/audits.component';
import { DetailsComponent } from './lists/details/details.component';
import { CategoriesComponent } from './categories/categories.component';
import { PermissionsComponent } from './permissions/permissions.component';
import { UserCalendarsComponent } from './user-calendars/user-calendars.component';
import { KpiReportComponent } from './kpi-report/kpi-report.component';
import { KpiSettingsComponent } from './kpi-settings/kpi-settings.component';
import { EventExclusionKeywordsComponent } from './event-exclusion-keywords/event-exclusion-keywords.component';
import { EmailCcConfigurationsComponent } from './email-cc-configurations/email-cc-configurations.component';

export const UiComponentsRoutes: Routes = [
  {
    path: '',
    children: [
      {
        path: 'lists',
        component: AppListsComponent,
      },
      {
        path: 'users',
        component: UsersComponent,
      },
      {
        path: 'roles',
        component: RolesComponent,
      },
      {
        path: 'categories',
        component: CategoriesComponent,
      },
      {
        path: 'permissions',
        component: PermissionsComponent,
      },
      {
        path: 'audits',
        component: AuditsComponent,
      },
      {
        path: 'customer-details/:id',
        component: DetailsComponent,
      },
      {
        path: 'user-calendars',
        component: UserCalendarsComponent
      },
      {
        path: 'kpi-report',
        component: KpiReportComponent
      },
      {
        path: 'kpi-settings',
        component: KpiSettingsComponent,
      },
      {
        path: 'event-exclusion-keywords',
        component: EventExclusionKeywordsComponent,
      },
      {
        path: 'email-cc-configurations',
        component: EmailCcConfigurationsComponent,
      },
    ],
  },
];


