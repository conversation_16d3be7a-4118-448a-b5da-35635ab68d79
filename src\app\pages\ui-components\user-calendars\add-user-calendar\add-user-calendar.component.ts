import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { RepositoryService } from 'src/app/shared/services/repository.service';
import { DialogService } from 'src/app/shared/services/dialog.service';
import { DataService } from 'src/app/shared/services/data.service';
import { UserCalendarForCreationDto } from 'src/app/_interface/user-calendar';

@Component({
  selector: 'app-add-user-calendar',
  templateUrl: './add-user-calendar.component.html'
})
export class AddUserCalendarComponent implements OnInit {
  public dataForm: FormGroup;

  constructor(
    private formBuilder: FormBuilder,
    private Ref: MatDialogRef<AddUserCalendarComponent>,
    private repoService: RepositoryService,
    private toastr: ToastrService,
    private dialogserve: DialogService,
    private dataService: DataService
  ) { }

  ngOnInit(): void {
    this.dataForm = this.formBuilder.group({
      name: ['', [Validators.required]],
      userName: ['', [Validators.required]]
    });
  }

  public validateControl = (controlName: string) => {
    return this.dataForm?.get(controlName)?.invalid && this.dataForm?.get(controlName)?.touched;
  }

  public hasError = (controlName: string, errorName: string) => {
    return this.dataForm?.get(controlName)?.hasError(errorName);
  }

  public createData = (dataFormValue: any) => {
    if (this.dataForm.valid) {
      this.executeDataCreation(dataFormValue);
    }
  };

  private executeDataCreation = (dataFormValue: any) => {
    let data: UserCalendarForCreationDto = {
      name: dataFormValue.name,
      userName: dataFormValue.userName
    };

    const apiUri: string = `api/usercalendars`;
    this.repoService.create(apiUri, data).subscribe(
      (res) => {
        this.dialogserve.openSuccessDialog("The calendar has been added successfully.")
        .afterClosed()
        .subscribe((res) => {
          this.dataService.triggerRefreshTab1();
          this.Ref.close([]);
        });
      },
      (error) => {
        this.toastr.error(error);
        this.Ref.close([]);
      }
    );
  };

  closeModal() {
    this.Ref.close([]);
  }
}