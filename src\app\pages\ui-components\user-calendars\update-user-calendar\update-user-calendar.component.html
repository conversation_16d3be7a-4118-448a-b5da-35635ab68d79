<h2 mat-dialog-title>Update Calendar</h2>

<form [formGroup]="dataForm" autocomplete="off" novalidate (ngSubmit)="createData(dataForm.value)">
  <mat-dialog-content>
    <div class="form-group">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>Name</mat-label>
        <input matInput type="text" placeholder="Name" formControlName="name">
        <mat-error *ngIf="hasError('name', 'required')">Name is required</mat-error>
      </mat-form-field>
    </div>

    <div class="form-group">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>User Name</mat-label>
        <input matInput type="text" placeholder="User Name" formControlName="userName">
        <mat-error *ngIf="hasError('userName', 'required')">User Name is required</mat-error>
      </mat-form-field>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="closeModal()">Cancel</button>
    <button mat-raised-button color="primary" [disabled]="!dataForm.valid">Update</button>
  </mat-dialog-actions>
</form>