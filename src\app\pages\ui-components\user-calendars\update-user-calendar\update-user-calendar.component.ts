import { Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { RepositoryService } from 'src/app/shared/services/repository.service';
import { DialogService } from 'src/app/shared/services/dialog.service';
import { DataService } from 'src/app/shared/services/data.service';
import { UserCalendarDto, UserCalendarForCreationDto } from 'src/app/_interface/user-calendar';

@Component({
  selector: 'app-update-user-calendar',
  templateUrl: './update-user-calendar.component.html'
})
export class UpdateUserCalendarComponent implements OnInit {
  public dataForm: FormGroup;
  userCalendar: UserCalendarDto;

  constructor(
    private formBuilder: FormBuilder,
    private Ref: MatDialogRef<UpdateUserCalendarComponent>,
    @Inject(MAT_DIALOG_DATA) public result: any,
    private repoService: RepositoryService,
    private toastr: ToastrService,
    private dialogserve: DialogService,
    private dataService: DataService
  ) { }

  ngOnInit(): void {
    this.dataForm = this.formBuilder.group({
      name: ['', [Validators.required]],
      userName: ['', [Validators.required]]
    });
    
    this.getUserCalendarToUpdate();
  }

  public validateControl = (controlName: string) => {
    return this.dataForm?.get(controlName)?.invalid && this.dataForm?.get(controlName)?.touched;
  }

  public hasError = (controlName: string, errorName: string) => {
    return this.dataForm?.get(controlName)?.hasError(errorName);
  }

  public createData = (dataFormValue: any) => {
    if (this.dataForm.valid) {
      this.executeDataCreation(dataFormValue);
    }
  };

  private executeDataCreation = (dataFormValue: any) => {
    let data: UserCalendarForCreationDto = {
      name: dataFormValue.name,
      userName: dataFormValue.userName
    };
    
    let id = this.result.id;
    const Uri: string = `api/usercalendars/${id}`;
    this.repoService.update(Uri, data).subscribe(
      (res) => {
        this.dialogserve.openSuccessDialog("The calendar has been updated successfully.")
        .afterClosed()
        .subscribe((res) => {
          this.dataService.triggerRefreshTab1();
          this.Ref.close([]);
        });
      },
      (error) => {
        this.toastr.error(error);
        this.Ref.close([]);
      }
    );
  };

  private getUserCalendarToUpdate = () => {
    let id = this.result.id;
    const Uri: string = `api/usercalendars/${id}`;
    this.repoService.getData(Uri)
      .subscribe({
        next: (calendar: any) => {
          this.userCalendar = { ...calendar };
          this.dataForm.patchValue(this.userCalendar);
        },
        error: (err) => {
          this.toastr.error(err);
        }
      });
  }

  closeModal() {
    this.Ref.close([]);
  }
}