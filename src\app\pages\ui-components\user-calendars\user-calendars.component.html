<mat-card class="cardWithShadow theme-card">
  <mat-card-header>
    <mat-card-title class="m-b-0">User Calendars</mat-card-title>
    <span class="flex-1-auto"></span>

    <button
      mat-flat-button
      color="primary"
      matTooltipPosition="left"
      class="m-l-8"
      matTooltipHideDelay="100000"
      (click)="addUserCalendar()">
      <mat-icon>add</mat-icon> <span fxHide.xs>Create new calendar</span>
    </button>
  </mat-card-header>

  <mat-card-content class="b-t-1">
    <div class="table-responsive m-t-16">
      <mat-form-field class="w-100" appearance="outline">
        <input matInput type="text" (keyup)="doFilter($any($event).target.value)" placeholder="Search....">
      </mat-form-field>

      <table mat-table [dataSource]="dataSource" class="w-100" matSort>
        <ng-container matColumnDef="action">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14">
            Action
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            <button
              mat-flat-button
              color="primary"
              [matMenuTriggerFor]="actions"
              class="m-t-8">
              Action<mat-icon>arrow_drop_down</mat-icon>
            </button>
            <mat-menu class="cardWithShadow" #actions="matMenu">
              <button mat-menu-item (click)="updateUserCalendar(element.id)">
                Edit
              </button>
              <button mat-menu-item (click)="deleteUserCalendar(element.id)">
                Delete
              </button>
            </mat-menu>
          </td>
        </ng-container>

        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14" mat-sort-header>
            Name
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            {{ element.name }}
          </td>
        </ng-container>

        <ng-container matColumnDef="userName">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14" mat-sort-header>
            User Name
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            {{ element.userName }}
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>

      <mat-paginator [pageSizeOptions]="[5, 10, 15]" showFirstLastButtons></mat-paginator>
    </div>
  </mat-card-content>
</mat-card>