import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatDialog } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { RepositoryService } from 'src/app/shared/services/repository.service';
import { DialogService } from 'src/app/shared/services/dialog.service';
import { DataService } from 'src/app/shared/services/data.service';
import { UserCalendarDto } from 'src/app/_interface/user-calendar';
import { AddUserCalendarComponent } from './add-user-calendar/add-user-calendar.component';
import { UpdateUserCalendarComponent } from './update-user-calendar/update-user-calendar.component';

@Component({
  selector: 'app-user-calendars',
  templateUrl: './user-calendars.component.html'
})
export class UserCalendarsComponent implements OnInit, AfterViewInit {
  displayedColumns: string[] = ['action', 'name', 'userName'];
  public dataSource = new MatTableDataSource<UserCalendarDto>();
  private refreshSubscription!: Subscription;
  
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  constructor(    
    private repoService: RepositoryService,
    private dialog: MatDialog,
    private toastr: ToastrService,
    private dataService: DataService,
    private dialogserve: DialogService
  ) { 
    this.refreshSubscription = this.dataService.refreshTab1$.subscribe(() => {
      this.getUserCalendars();
    });
  }
  
  ngOnInit(): void {
    this.getUserCalendars();
  }

  public getUserCalendars() {
    this.repoService.getData('api/usercalendars')
    .subscribe(res => {
      this.dataSource.data = res as UserCalendarDto[];
    },
    (err) => {
      console.log(err);
    });
  }

  ngAfterViewInit(): void {
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  };

  addUserCalendar() {
    const popup = this.dialog.open(AddUserCalendarComponent, {
      width: '500px', height: '300px',
      enterAnimationDuration: '100ms',
      exitAnimationDuration: '100ms',
    });
  }

  updateUserCalendar(id: string) {
    const popup = this.dialog.open(UpdateUserCalendarComponent, {
      width: '500px', height: '300px',
      enterAnimationDuration: '100ms',
      exitAnimationDuration: '100ms',
      data: {
        id: id
      }
    });
  }

  deleteUserCalendar(id: any) {
    this.dialogserve.openConfirmDialog('Are you sure you want to delete this calendar?')
      .afterClosed()
      .subscribe((res) => {
        if (res) {
          const deleteUri: string = `api/usercalendars/${id}`;
          this.repoService.delete(deleteUri).subscribe((res) => {
            this.dialogserve.openSuccessDialog("The calendar has been deleted successfully.")
            .afterClosed()
            .subscribe((res) => {
              this.getUserCalendars();
            });
          });
        }
      });
  }
}