<mat-card class="cardWithShadow theme-card modal-card">
  <mat-card-header>
    <mat-card-title class="m-b-0">Create user</mat-card-title>
    <span class="flex-1-auto"></span>
    <button mat-flat-button matTooltipPosition="left" class="m-l-8" matTooltipHideDelay="100000"
      (click)="closeModal()">
      <mat-icon color="warn">close</mat-icon>
    </button>
  </mat-card-header>
  <mat-card-content class="b-t-1">
    <form [formGroup]="dataForm" autocomplete="off" novalidate (ngSubmit)="createData(dataForm.value)">

      <mat-form-field class="w-100" appearance="outline">
        <mat-label>First Name</mat-label>
        <input matInput formControlName="firstName" />
      </mat-form-field>
      <mat-form-field class="w-100" appearance="outline">
        <mat-label>Last Name</mat-label>
        <input matInput formControlName="lastName" />
      </mat-form-field>

      <mat-form-field class="w-100" appearance="outline">
        <mat-label>User Name</mat-label>
        <input matInput formControlName="userName" />
      </mat-form-field>
      <mat-form-field class="w-100" appearance="outline">
        <mat-label>Email</mat-label>
        <input matInput type="email" formControlName="email" />
        <mat-error *ngIf="validateControl('email') && hasError('email', 'email')">Please provide a valid email</mat-error>
        <mat-error *ngIf="validateControl('email') && hasError('email', 'required')">Email is required</mat-error>
      </mat-form-field>


      <mat-label>Roles</mat-label>
      <div *ngFor="let role of roles">
        <mat-checkbox [checked]="isSelected(role.name)" (change)="toggleRoleSelection($event, role.name)">
          {{ role.name }}
        </mat-checkbox>
      </div>



      <button mat-flat-button color="primary" [disabled]="!dataForm.valid">
        Save
      </button>
    </form>
  </mat-card-content>
</mat-card>