<mat-card class="cardWithShadow theme-card">
  <mat-card-header>
    <mat-card-title class="m-b-0">Users</mat-card-title>
    <span class="flex-1-auto"></span>

    <button
    mat-flat-button
    color="primary"
    matTooltipPosition="left"
    class="m-l-8"
    matTooltipHideDelay="100000"
    (click)="addUser()">
    <mat-icon>add</mat-icon>  <span fxHide.xs>Create new user</span>
    </button>
  </mat-card-header>



  <mat-card-content class="b-t-1">
            <div class="table-responsive m-t-16">
              <mat-form-field class="w-100" appearance="outline">
                <input matInput type="text" (keyup)="doFilter($any($event).target.value)" placeholder="Search....">
              </mat-form-field>


              <table mat-table [dataSource]="dataSource" class="w-100" matSort>
                <ng-container matColumnDef="action">
                  <th
                    mat-header-cell
                    *matHeaderCellDef
                    class="f-w-600 mat-subtitle-1 f-s-14"                 
                  >
                  Action 
                  </th>
                  <td mat-cell *matCellDef="let element" class="mat-body-1">
                    <button
                    mat-flat-button
                    color="primary"
                    [matMenuTriggerFor]="actions"
                    class="m-t-8"
                  >
                    Action<mat-icon>arrow_drop_down</mat-icon>
                  </button>
                  <mat-menu class="cardWithShadow" #actions="matMenu">
                    <button mat-menu-item  (click)="updateUser(element.id)">
                      Edit
                    </button>
                    <button mat-menu-item (click)="DeleteUser(element.id)" >
                     Delete
                    </button>
                  </mat-menu>
                  </td>
                </ng-container>
                <ng-container matColumnDef="username">
                  <th
                    mat-header-cell
                    *matHeaderCellDef
                    class="f-w-600 mat-subtitle-1 f-s-14"
                    mat-sort-header
                  >
                  User Name
                  </th>
                  <td mat-cell *matCellDef="let element" class="mat-body-1">
                    {{ element.userName }}
                  </td>
                </ng-container>
        <ng-container matColumnDef="name">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="f-w-600 mat-subtitle-1 f-s-14"
            mat-sort-header
          >
          First Name
          </th>
          <td mat-cell *matCellDef="let element" class="mat-body-1">
            {{ element.firstName}}
          </td>
        </ng-container>
                <ng-container matColumnDef="surname">
                  <th
                    mat-header-cell
                    *matHeaderCellDef
                    class="f-w-600 mat-subtitle-1 f-s-14"
                    mat-sort-header
                  >
                  Last Name
                  </th>
                  <td mat-cell *matCellDef="let element" class="mat-body-1">
                    {{ element.lastName }}
                  </td>
                </ng-container>
            <ng-container matColumnDef="roles">
            <th
              mat-header-cell
              *matHeaderCellDef
              class="f-w-600 mat-subtitle-1 f-s-14"
            >
            Roles
            </th>
            <td mat-cell *matCellDef="let element" class="mat-body-1">
              {{ element.roles }}
            </td>
          </ng-container>
           <ng-container matColumnDef="email">
            <th
              mat-header-cell
              *matHeaderCellDef
              class="f-w-600 mat-subtitle-1 f-s-14"
              mat-sort-header
            >
          Email
            </th>
            <td mat-cell *matCellDef="let element" class="mat-body-1">
              {{ element.email }}
            </td>
          </ng-container>

          <ng-container matColumnDef="emailconfirm">
            <th
              mat-header-cell
              *matHeaderCellDef
              class="f-w-600 mat-subtitle-1 f-s-14"
            >
              Email confirm
            </th>
            <td mat-cell *matCellDef="let element">
              <ng-template [ngIf]="element.emailConfirmed == 'True'">
                <span
                  class="bg-light-accent text-accent rounded f-w-600 p-6 p-y-4 f-s-12"
                >
                Yes
                </span>
              </ng-template>
              <ng-template [ngIf]="element.emailConfirmed == 'False'">
                <span
                class="bg-light-warning text-warning rounded f-w-600 p-6 p-y-4 f-s-12"
              >
               No
              </span>
              </ng-template>
            </td>
          </ng-container>
                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
              </table>
            </div>

            <mat-paginator [pageSize]="4" [pageSizeOptions]="[4, 6, 10, 20]">
            </mat-paginator>
  </mat-card-content>
</mat-card>

