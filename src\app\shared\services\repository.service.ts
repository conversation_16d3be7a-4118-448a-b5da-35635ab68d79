import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';


@Injectable({
  providedIn: 'root'
})
export class RepositoryService {

  constructor(private http: HttpClient) { }

  public getData = (route: string) => {
    return this.http.get(this.createCompleteRoute(route, environment.apiUrl));
  }
 
  public create = (route: string, body:any) => {
    return this.http.post(this.createCompleteRoute(route, environment.apiUrl), body, this.generateHeaders());
  }
  public createWithData = (route: string) => {
    return this.http.post(this.createCompleteRoute(route, environment.apiUrl), this.generateHeaders());
  }
  public update = (route: string, body:any) => {
    return this.http.put(this.createCompleteRoute(route, environment.apiUrl), body, this.generateHeaders());
  }
 
  public delete = (route: string) => {
    return this.http.delete(this.createCompleteRoute(route, environment.apiUrl));
  }
 
  public exportReport(route: string, params?: { [key: string]: any }): Observable<Blob> {
    let httpParams = new HttpParams();
    if (params) {
      Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined) {
          httpParams = httpParams.set(key, params[key].toString());
        }
      });
    }
    return this.http.get(this.createCompleteRoute(route, environment.apiUrl), {
      params: httpParams,
      responseType: 'blob' // Set responseType to 'blob' for file downloads
    });
  }
 public getDataWithoutLoading = (route: string) => {
    const headers = new HttpHeaders().set('skipLoading', 'true');
    return this.http.get(this.createCompleteRoute(route, environment.apiUrl), {
        headers: headers
    });
}
  private createCompleteRoute = (route: string, envAddress: string) => {
    return `${envAddress}/${route}`;
  }
 
  private generateHeaders = () => {
    return {
      headers: new HttpHeaders({'Content-Type': 'application/json'})
    }
  }
}

