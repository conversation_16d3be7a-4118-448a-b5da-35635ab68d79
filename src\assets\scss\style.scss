@use "@angular/material" as mat;
@include mat.core();

@import "variables";
@import "layouts/transitions";
@import "helpers/color";
@import "helpers/icon-size";

//Theme colors

@import "themecolors/default_theme";
@include mat.all-component-themes($bluetheme);
//container layout
@import "overrides/materialoverrides";
@import "container";
@import "layouts/layouts";
@import "grid/grid";
@import "helpers/custom-flex";
@import "helpers/index";


// pages
@import "pages/dashboards";
@import "pages/auth";


// components
.btn-save{
    width: 20%;
    float:right;
}
// Tách biệt cột Đầu và Cuối
.sticky-column {
    position: sticky;
    left: 0; /* <PERSON><PERSON>m bảo cột luôn dính về phía bên trái */
    background-color: #fff; /* <PERSON><PERSON>u nền cho cột */
    z-index: 10; /* <PERSON><PERSON><PERSON> bảo cột nằm trên các phần khác */
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.2); /* <PERSON><PERSON><PERSON> bóng bên phải */
    border-right: 1px solid #ddd; /* T<PERSON>o đường viền để phân biệt */
  }
.stickyend-column {
    position: sticky;
    right: 0; /* Dính về phía bên phải */
    background-color: #fff; /* Màu nền */
    z-index: 10; /* Đảm bảo cột nằm trên các phần khác */
    box-shadow: -1px 0 5px rgba(0, 0, 0, 0.2); /* Bóng bên trái cột */
    border-left: 1px solid #ddd; /* Viền bên trái để phân biệt */
  }
  .form-row {
    display: flex;
    gap: 16px; /* Adds space between inputs */
    align-items: center;
  
    .w-50 {
      flex: 1; /* Ensures both inputs share equal space */
    }
  }